import { DropdownButton } from 'react-bootstrap';
import DropdownItem from 'react-bootstrap/esm/DropdownItem';


interface IDropDownProps {
    selectedItem: string;
    setSelectedItem: (value: string) => void;
    items: string[];
}
function DropDown({ selectedItem, items, setSelectedItem }: IDropDownProps) {
    return (
        <DropdownButton as="span"
            title={selectedItem}
            id="dropdown-basic-card" className="d-inline-block p-0" style={{ backgroundColor: "#fff" }}>
            {items && items.map((item: string, index: number) => {
                return (
                    <DropdownItem
                        key={index}
                        onClick={() => setSelectedItem(item)}
                    >{item}</DropdownItem>
                )
            })}
        </DropdownButton>
    );
}

export default DropDown;