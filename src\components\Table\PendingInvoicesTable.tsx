import moment from "moment";
import { Table } from "react-bootstrap"

interface ITherapistTable {
    tableData: any,
    selectedIds: any,
    setSelectedIds: any
}

export default function PendingInvoiceTable({ tableData, selectedIds, setSelectedIds }: ITherapistTable) {

    const handleSelect = (e: any, id: any) => {
        if (e.target.checked) {
            setSelectedIds([...selectedIds, id])
        } else {
            const filteredIds = selectedIds.filter((data: any) => data !== id)
            setSelectedIds(filteredIds)
        }
        // console.log(selectedIds)
    }

    const handleSelectAll = (e: any) => {
        if (e.target.checked) {
            const ids = tableData.map((data: any) => data._id)
            setSelectedIds(ids)
        } else {
            setSelectedIds([])
        }
    };

    const isCheckboxChecked = (id: any) => {
        return selectedIds.includes(id);
    }

    const formattedDate = (date: any) => {
        return moment(date).format("DD-MM-YYYY")
    }
    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            {/* <th>
                                <input type="checkbox" onChange={handleSelectAll} />
                            </th> */}
                            <th>Sr.No</th>
                            <th>Invoice Serial No.</th>
                            <th>Session Date</th>
                            <th>Link Value</th>
                            {/* <th>Gateway Charges</th> */}
                            <th>Payout</th>

                        </tr>
                    </thead>
                    <tbody>
                        {tableData && tableData.length > 0 ? tableData.map((data: any, index: number) => {
                            const reccurance = data?.scheduleId?.recurrenceDates?.find((data_rec: any) => String(data_rec._id) === String(data?.scheduleRecId))
                            return (
                                <tr>
                                    {/* <td>
                                        <input type="checkbox" onChange={(e: any) => { handleSelect(e, data._id) }} checked={isCheckboxChecked(data._id)} />
                                    </td> */}
                                    <td>{index + 1}</td>
                                    <td>{data?.invoiceSerialNumber}</td>
                                    <td> { reccurance?.fromDate ? formattedDate(reccurance?.fromDate) : " -- "}</td>
                                    <td>{data?.actualValue || 0}</td>
                                    {/* <td className="text-primary">Fee: {data?.gatewayCharges?.gatewayFee || "--"}, Tax: {data?.gatewayCharges?.gatewayTax || "--"}</td> */}
                                    <td>{data?.invoiceValue}</td>

                                </tr>
                            )
                        }) :
                            <tr>
                                <td colSpan={6} className="text-center">No data found</td>
                            </tr>
                        }
                    </tbody>
                </Table>
            </div>
        </div>
    )
}