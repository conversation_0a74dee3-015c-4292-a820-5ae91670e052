import { ChangeEvent, useState } from 'react'
import { Modal, Button, Form, OverlayTrigger, Tooltip } from 'react-bootstrap'

interface AppointmentModalProps {
    show: boolean;
    setShow: (show: boolean) => void;
    rescheduleId?: string;
    setRescheduleId?: (id: string) => void;
}

interface FormData {
    email: string;
    date: string;
    startTime: string;
    endTime: string;
    recurrence: string;
    payment: string;
    clientCountry: string;
    name: string;
    description: string;
    toDate?: string;
    phone: string;
}

export default function ScheduleAppointmentModal({ show, setShow, rescheduleId }: AppointmentModalProps) {
    const [formData, setFormData] = useState<FormData>({
        email: '',
        date: '',
        startTime: '',
        endTime: '',
        recurrence: '',
        payment: '',
        clientCountry: '',
        name: '',
        description: '',
        toDate: '',
        phone: '',
    });

    const [errors, setErrors] = useState<string>();
    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    }

    const handleSelectChange = (e: ChangeEvent<HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    }

    const validateForm = () => {
        const { name, email, description, date, startTime, endTime, recurrence, clientCountry } = formData;
        return name && email && description && date && startTime && endTime && recurrence && clientCountry;
    }


    const renderTooltip = (props : any) => (
        <Tooltip id="button-tooltip" {...props}>
            {validateForm() ? "Schedule an appointment" : "Please Fill all the required fields"}
        </Tooltip>
    );

    return (
        <>
            <Modal show={show} onHide={
                () => {setShow(false);}
            }>
                <Modal.Header className="border-0" closeButton>
                    <Modal.Title >{rescheduleId ? "Reschedule an appointment" : "Schedule an appointment"}</Modal.Title>
                </Modal.Header>
                <Modal.Body className="pt-0">
                    <div className="form-group mb-3">
                        <label>Select Client <span className="text-danger">*</span></label>
                        <Form.Control type="text" className="form-control" name='name' value={formData.name} onChange={handleChange} required disabled={rescheduleId !== ""}>
                        </Form.Control>
                    </div>
                    <div className="form-group mb-3">
                        <label>Email Address <span className="text-danger">*</span></label>
                        <input type="email" className="form-control" name='email' value={formData.email} onChange={handleChange} required disabled={rescheduleId !== ""}>
                        </input>
                    </div>
                    <div className="form-group mb-3">
                        <label>Date <span className="text-danger">*</span></label>
                        <input type="date" className="form-control" name='date' value={formData.date} onChange={handleChange} required min={new Date().toISOString().slice(0, -14)}>
                        </input>
                    </div>

                    <div className="row mb-3">
                        <div className="col-12">
                            <label>Time <span className="text-danger">*</span></label>
                        </div>
                        <div className="col-6">
                            <input type="time" className="form-control" name='startTime' value={formData.startTime} onChange={handleChange} required disabled={rescheduleId !== ""}>
                            </input>
                        </div>
                        <div className="col-6">
                            <input type="time" className="form-control" name='endTime' value={formData.endTime} onChange={handleChange} required disabled={rescheduleId !== ""}>
                            </input>
                        </div>
                    </div>

                    <div className="form-group mb-3">
                        <label>Your time zone</label>
                        <div className="p-3 rounded border info-box">
                            <div> Time zone in Delhi (GMT+5:30)</div>
                            Thursday, 31 August 2023, 11:38 pm
                        </div>
                    </div>

                    <div className="form-group mb-3">
                        <label>Client Country</label>
                        <select className="form-control" name='clientCountry' defaultValue={'India'} value={formData.clientCountry} onChange={handleSelectChange} required disabled={rescheduleId !== ""}>
                            <option selected>
                                Select Country
                            </option>
                            <option>
                                India
                            </option>
                            <option>
                                United Kingdom
                            </option>
                        </select>
                    </div>

                    <div className="form-group mb-3">
                        <label>Client time zone</label>
                        <div className="p-3 rounded border info-box">
                            <div> Time zone in Delhi (GMT+5:30)</div>
                            Thursday, 31 August 2023, 11:38 pm
                        </div>
                    </div>


                    <div className="form-group mb-3">
                        <label>Recurrence</label>
                        <select className="form-control" name='recurrence' value={formData.recurrence} onChange={handleSelectChange} disabled={rescheduleId !== ""}>
                            <option selected value="select">
                                Select recurrence
                            </option>
                            <option value='daily' >
                                Daily
                            </option>
                            <option value='weekly' >
                                Weekly
                            </option>
                        </select>
                    </div>

                    {
                        formData.recurrence && formData.recurrence !== "select" && <div className="form-group mb-3">
                            <label>Date </label>
                            <input type="date" className="form-control" name='toDate' value={formData.toDate} onChange={handleChange} min={new Date().toISOString().slice(0, -14)}>
                            </input>
                        </div>
                    }
                    <div className="form-group mb-3">
                        <label>Payment</label>
                        <input className="form-control" type='number' name='payment' value={formData.payment} onChange={handleChange} disabled={rescheduleId !== ""}></input>
                    </div>

                    <OverlayTrigger
                        placement="top"
                        delay={{ show: 250, hide: 400 }}
                        overlay={renderTooltip}
                    >
                        <div className="form-group mb-2">
                            <Button variant="primary" className="col-md-12 justify-content-center" onClick={() => {}} disabled={!validateForm()}>
                                <span className="pe-2">{rescheduleId? "Save changes":"Schedule"}</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M17.2063 10.3313L11.5813 15.9563C11.4924 16.0391 11.3749 16.0842 11.2534 16.082C11.132 16.0799 11.0161 16.0307 10.9302 15.9448C10.8444 15.8589 10.7952 15.743 10.793 15.6216C10.7909 15.5002 10.8359 15.3826 10.9187 15.2938L15.743 10.4688H3.125C3.00068 10.4688 2.88145 10.4194 2.79354 10.3315C2.70564 10.2436 2.65625 10.1243 2.65625 10C2.65625 9.87571 2.70564 9.75648 2.79354 9.66857C2.88145 9.58066 3.00068 9.53128 3.125 9.53128H15.743L10.9187 4.70628C10.8359 4.61742 10.7909 4.49989 10.793 4.37845C10.7952 4.25701 10.8444 4.14115 10.9302 4.05526C11.0161 3.96938 11.132 3.92018 11.2534 3.91804C11.3749 3.9159 11.4924 3.96098 11.5813 4.04378L17.2063 9.66878C17.294 9.75667 17.3433 9.87581 17.3433 10C17.3433 10.1242 17.294 10.2434 17.2063 10.3313Z" fill="#2C2C2C" />
                                </svg>
                            </Button>
                        </div>
                    </OverlayTrigger>
                    <div className="form-group mb-3">
                        {errors && <label className='text-danger'>{errors}</label>}
                    </div>

                </Modal.Body>
            </Modal>
        </>
    )
}
