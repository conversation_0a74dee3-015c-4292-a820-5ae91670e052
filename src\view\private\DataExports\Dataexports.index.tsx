import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Col, Form, Row } from "react-bootstrap";
import Select from "react-select"
import { TherapistService } from "../../../services/therapist.service";
import DatePicker from "react-datepicker";
import moment from "moment";
import toast from "react-hot-toast";
import { AdminServices } from "../../../services/admin.service";
import ExportDataTable from "../../../components/Table/DataExportTable";
import csvDownload from "json-to-csv-export";


export default function DataExportIndex() {

    const [therapist, setTherapist] = useState<any>();
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [selectStatus, setSelectStatus] = useState<any>(undefined);
    const [tableData, setTableData] = useState<any[]>([]);
    const [pageSize, setPageSize] = useState<number>(10);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [selectedDataType, setSelectedDataType] = useState<any>({});


    let dataTypeOptions = [
        {
            value: 'invoices',
            label: 'Invoices'
        }
    ]


    const [dateRange, setDateRange] = useState([
        moment().subtract(7, 'days').toDate(),
        new Date()
    ]);


    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(1, 999).then((res) => {
            if (res.status === 200) {
                setTherapist(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }

    async function getExportData() {
        if(!selectTherapist){
            toast.error("Please select therapist");
            return;
        }
        if(!selectedDataType.value){
            toast.error('Please select data type');
            return;
        }
        if(!dateRange[0] || !dateRange[1]){
            toast.error('Please select date range');
            return;
        }

        let payload = {
            therapistId: selectTherapist,
            startDate: moment(dateRange[0]).format("DD-MM-YYYY"),
            endDate: moment(dateRange[1]).format("DD-MM-YYYY"),
            type: selectedDataType.value
        }
        
        await AdminServices.getExportedData(payload)
            .then((res) => {
                if(res.status === 200){
                    setTableData(res.data?.export_data || []);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }


    useEffect(() => {
        getAllTherapist();
    }, [])

    const handleDownload = async () => {
        const dataToConvert = {
            data: tableData,
            filename: 'invoices',
            delimiter: ',',
            headers: ['Type', "Id", "Client Id", "Total", "Commission", "Discount", "Amount", "Invoice Date", "Payout Id", "Is Cancelled"]
        }
        csvDownload(dataToConvert);
    }

    return (
        <div>
            <h5>
                Exports
            </h5>

            <Row className="mt-3">
                <Col md={4}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Select Therapist</Form.Label>
                        <Select
                            options={therapist}
                            onChange={(e: any, action: any) => {
                                if (action.action === 'clear') {
                                    setSelectTherapist(undefined);
                                    return;
                                }
                                setSelectTherapist(e.value)
                            }}
                            isClearable
                        />
                    </Form.Group>

                </Col>
                <Col md={4}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">
                            Date Range
                        </Form.Label>
                        <DatePicker
                            selected={dateRange[0]}
                            onChange={(e) => setDateRange(e)}
                            startDate={dateRange[0]}
                            endDate={dateRange[1]}
                            selectsRange
                            className="form-control"
                        />
                    </Form.Group>
                </Col>

                <Col md={4}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Select Data Type</Form.Label>
                        <Select
                            options={dataTypeOptions}
                            onChange={(e: any, action: any) => {
                                if (action.action === 'clear') {
                                    setSelectedDataType(undefined);
                                    return;
                                }
                                setSelectedDataType(e)
                            }}
                            isClearable
                        />
                    </Form.Group>
                </Col>
            </Row>
            <Row>
                <Col md={12}>
                    <Button className="float-end" size="sm" variant="primary" onClick={getExportData} >
                        View Data
                    </Button>
                </Col>
            </Row>

            <Card className="mt-3 mb-3 shadow">
                {
                    tableData && tableData.length > 0 ?
                        <>
                            <Row className="mt-2 pe-3">
                                <Col md={12}>
                                    <Button className="float-end" size="sm" variant="primary" onClick={handleDownload} >
                                        Export as CSV
                                    </Button>
                                </Col>
                            </Row>
                            <ExportDataTable tableData={tableData} />
                        </> :
                        <div className="text-center p-3">
                            <span>No Data Found</span>
                        </div>
                }
            </Card> 
        </div>
    )
}