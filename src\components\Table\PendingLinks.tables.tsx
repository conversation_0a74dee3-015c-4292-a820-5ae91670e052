import moment from "moment";
import { useEffect, useState } from "react";
import { Button, Table } from "react-bootstrap"
import { TherapistService } from "../../services/therapist.service";
import ConfirmationModal from "../Modals/Confirmation.modal";

interface ITherapistTable {
    therapistId: string
}

export default function PendingLinksTable({ therapistId }: ITherapistTable) {

    const [data, setData] = useState<any>([]);

    const [showConfirmatiomModal, setShowConfirmationModal] = useState<any>(undefined)

    async function getTherapistPendingTransActions() {
        await TherapistService.getTherapistPendingTransActions(therapistId)
            .then((res) => {
                if (res.status === 200) {
                    setData(res.data?.transactions)
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }

    const formattedDate = (date: any) => {
        return moment(date).format("DD-MM-YYYY")
    }

    useEffect(() => {
        if (therapistId) {
            getTherapistPendingTransActions()
        }
    }, [therapistId]);

    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Amount</th>
                            <th>Schedule Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {data && data.length > 0 ? data.map((data: any, index: number) => {
                            return (
                                <tr>
                                    <td>{index + 1}</td>
                                    <td>{data?.amount}</td>
                                    <td>{formattedDate(data?.schedule?.fromDate)}</td>
                                    <td><Button variant="outline-danger" size="sm" onClick={() => setShowConfirmationModal(data._id)}>Delete</Button></td>
                                </tr>
                            )
                        }) :
                            <tr>
                                <td colSpan={6} className="text-center">No data found</td>
                            </tr>
                        }
                    </tbody>
                </Table>
            </div>

            <ConfirmationModal
                show={showConfirmatiomModal}
                handleClose={() => setShowConfirmationModal(undefined)}
                reload={getTherapistPendingTransActions}
            />
        </div>
    )
}