import { Row, Col, Container, Tab, Tabs, Pagination } from "react-bootstrap";
import DataCard from "../../../components/Cards/DataCard";
import CustomButton from "../../../components/CustomButton";
import { useEffect, useState } from "react";
import TableIndex from "../../../components/Table/table.index";
import ScheduleAppointmentModal from "../../../components/Modals/ScheduleAppointmentModal";
import { TherapistService } from "../../../services/therapist.service";
import AppointmentTable from "../../../components/Table/AppointmentsTable";

export default function Appointment() {
    // const [tab, setTab] = useState('all');
    // const [show, setShow] = useState(false);
    const [sessions, setSessions] = useState([])
    const [pageNumber, setPageNumber] = useState<number>(1)
    const [pageSize, setPageSize] = useState<number>(10)


    let items: any = [];

    for (let number = 1; number <= pageNumber; number++) {
        items.push(
            <Pagination.Item key={number} active={number === pageNumber}>
                {number}
            </Pagination.Item>
        );
    }
    const getSessions = async () => {
        await TherapistService.getAllSessionsById(pageNumber, pageSize).then((res: any) => {
            setSessions(res.data)
        })
            .catch((err: any) => {
                console.log(err)
            }
            )
    }

    useEffect(() => {
        getSessions()
    }, [pageNumber, pageSize])

    return (
        <Container>
            <Row >
                <Col >
                    <DataCard title={"Therapist"} text={"View All"} subTitle={"Total"} value={"120"} color="#F5C8BD" />
                </Col>

            </Row>

            <Row>
                <Col className='d-flex justify-content-between'>
                    {/* <Tabs
                        id="controlled-tab-example"
                        activeKey={tab}
                        onSelect={(k) => k && setTab(k)}
                        className="m-3"
                    >
                        <Tab eventKey="all" title="All" />
                        <Tab eventKey="recived" title="Recived" />
                        <Tab eventKey="pending" title="Pending" />
                    </Tabs> */}

                    
                </Col>
            </Row>
            <Row>
               <AppointmentTable tableData={sessions} />
            </Row>
            <div className="px-0 px-md-3 mt-2">
                        <div className="d-flex flex-column flex-md-row justify-content-md-between">
                            <div>
                                <Pagination>
                                    <Pagination.Prev onClick={() => setPageNumber(pageNumber - 1)} />
                                    {items}
                                    <Pagination.Next onClick={() => setPageNumber(pageNumber + 1)} />
                                </Pagination>
                            </div>
                            <div>
                                <div className='row'>
                                    <div className="col pe-md-0">
                                        <span className="small flex-grow-1">Record Per Page </span>
                                    </div>
                                    <div className="col-auto">
                                        <select className="form-select bg-light form-select-sm" aria-label=".form-select-sm example" onChange={(e: any) => { setPageSize(e.target.value) }}>
                                            
                                            <option value="1">10</option>
                                            <option value="2">15</option>
                                            <option value="3">20</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
           
        </Container>
    )
}

