import { <PERSON><PERSON>, <PERSON>, Col, Form, Pagination, Row } from "react-bootstrap";
import Select from "react-select";
import { TherapistService } from "../../../services/therapist.service";
import { useEffect, useState } from "react";
import TherapistTable from "../../../components/Table/Therapist.table";
import { PaymentService } from "../../../services/payment.service";
import PendingInvoiceTable from "../../../components/Table/PendingInvoicesTable";
import PendingDeductionsTable from "../../../components/Table/PendingDeductionsTable";
import PayoutTable from "../../../components/Table/Payout.table";
import toast from "react-hot-toast";
import csvDownload from "json-to-csv-export";

const statuses = [
    {
        label: "Pending",
        value: "PENDING"
    },
    {
        label: "Paid",
        value: "PAID"
    },
    {
        label: "Cancelled",
        value: "CANCELLED"
    }
]

export default function Transactions() {
    const [therapist, setTherapist] = useState<any>();
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [show, setShow] = useState<boolean>(false);

    const [payouts, setPayouts] = useState<any>();

    const [selectStatus, setSelectStatus] = useState<any>(undefined);

    const [invoice, setInvoice] = useState<any>();
    const [selectedInvoiceIds, setSelectedInvoiceIds] = useState<any>([]);
    const [csvData, setCsvData] = useState<any>([]);
    const [deduction, setDeduction] = useState<any>([]);
    const [selectedDeductionIds, setSelectedDeductionIds] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [totalCount, setTotalCount] = useState({
        invoice: 0,
        deduction: 0,
        comission: 10,
        final: 0
    });
    let items: any = [];

    for (let number = 1; number <= pageNumber; number++) {
        items.push(
            <Pagination.Item key={number} active={number === pageNumber}>
                {number}
            </Pagination.Item>
        );
    }

    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(1, 999).then((res) => {
            if (res.status === 200) {
                setTherapist(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }


    const getPayouts = async () => {
        await PaymentService.getPayouts(1, 999999, selectTherapist, selectStatus).then((res) => {
            if (res.status === 200) {
                setPayouts(res.data.payouts)
                console.log(res.data.payouts, "payouts")
            }
        }).catch(e => {
            console.log(e);
        })
    }

    useEffect(() => {
        getAllTherapist()
    }, [])

    useEffect(() => {
        getPayouts()
    }, [selectTherapist, selectStatus]);

    const getCounts = () => {
        let invoiceTotal = 0;
        let deductionTotal = 0;

        invoice && invoice.forEach((element: any) => {
            invoiceTotal += parseInt(element.invoiceValue);
        });

        deduction && deduction.forEach((element: any) => {
            deductionTotal += parseInt(element.amount);
        });

        const commission = totalCount.comission / 100;
        let finalAmount = (invoiceTotal - deductionTotal) - ((invoiceTotal - deductionTotal) * commission);

        setTotalCount({
            invoice: invoiceTotal,
            deduction: deductionTotal,
            comission: totalCount.comission,
            final: finalAmount
        });
    };

    const getCSVData = async () => {
        await PaymentService.getPendingPayout()
            .then((res) => {
                if (res.status === 200) {
                    setCsvData(res.data.csvData);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }
    const handleDownload = async () => {
        const dataToConvert = {
            data: csvData,
            filename: 'pending-payouts',
            delimiter: ',',
            headers: ['Therapist Name', "Therapist UPI Id", "Amount"]
        }
        csvDownload(dataToConvert);
    }

    useEffect(() => {
        getCSVData();
    }, []);
    useEffect(() => {
        getCounts();
    }, [invoice, deduction, totalCount.comission]);
    return (
        <>
            <h5>
                Transactions
            </h5>
            <Row>
                <div className="d-flex mb-3 justify-content-end">
                    <button className="shadow btn btn-success" onClick={handleDownload}>
                        Download Pending Payouts
                    </button>
                </div>
                <Row>
                    <Col>
                        <h6>
                            Filters
                        </h6>
                    </Col>
                </Row>

                <Row>
                    <Col md={4}>
                        <Form.Group className="p-2 mb-3 card shadow border-0">
                            <Form.Label className="text-muted">Select Therapist</Form.Label>
                            <Select
                                options={therapist}
                                onChange={(e: any, action: any) => {
                                    if (action.action === 'clear') {
                                        setSelectTherapist(undefined);
                                        return;
                                    }
                                    setSelectTherapist(e.value)
                                }}
                                isClearable
                            />
                        </Form.Group>

                    </Col>
                    <Col md={4}>
                        <Form.Group className="p-2 mb-3 card shadow border-0">
                            <Form.Label className="text-muted">Select Status</Form.Label>
                            <Select
                                options={statuses}
                                onChange={(e: any, action: any) => {
                                    if (action.action === 'clear') {
                                        setSelectStatus(undefined);
                                        return;
                                    }
                                    setSelectStatus(e.value)
                                }}
                                isClearable
                            />
                        </Form.Group>
                    </Col>
                </Row>

                <Card className="mt-3 mb-3 shadow">
                    <Row>
                        <Col>
                            <div className="mt-3 fw-bold">All Transactions</div>
                            <div>
                                <PayoutTable tableData={payouts} reload={getPayouts} />
                            </div>
                        </Col>
                    </Row>
                </Card>
            </Row>
        </>
    )
}