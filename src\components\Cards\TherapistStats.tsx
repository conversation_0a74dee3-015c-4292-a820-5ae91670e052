import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Card, Col, Form, Row } from 'react-bootstrap'
import { useParams } from 'react-router-dom';
import { TherapistService } from '../../services/therapist.service';
import ReactDatePicker from 'react-datepicker';
import moment from 'moment';
import ClientListModal from '../Modals/ClientListModal';

export default function TherapistStats() {

  const [stats, setStats] = useState<any>({});
  const { id } = useParams();
  const [dateRange, setDateRange] = useState([
    moment().subtract(30, 'days').toDate(),
    new Date()
  ]);
  const [show, setShow] = useState<boolean>(false);

  // fromDate=2023-01-01&toDate=2024-09-30
  async function getTherapistStats() {
    await TherapistService.getTherapistDetailStats(id, moment(dateRange[0]).format("YYYY-MM-DD"), moment(dateRange[1]).format("YYYY-MM-DD"))
      .then((res) => {
        if (res.status === 200) {
          setStats(res.data);
          // console.log(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
  }

  useEffect(() => {
    getTherapistStats();
  }, [dateRange])
  return (
    <>
      <Card className="mt-3">
        <Card.Body>

          <Card.Text className="mt-3">
            <div>
              <b className="fs-4 p-0">Stats:</b>
            </div>
            <Row>
              <Col md={4}>
                <Form.Group className="p-2 mb-3 card border-0">
                  <Form.Label className="text-muted">
                    Date Range
                  </Form.Label>
                  <ReactDatePicker
                    selected={dateRange[0]}
                    onChange={(e) => setDateRange(e)}
                    startDate={dateRange[0]}
                    endDate={dateRange[1]}
                    selectsRange
                    className="form-control"
                    dateFormat={"dd/MM/yyyy"}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Card.Text>

          <div className="float-end">
            <Button
              size="sm"
              variant='outline-secondary'
              onClick={() => setShow(true)}
            >
              View All Clients
            </Button>
          </div>
          <Card.Text>
            <p className='mb-0 fs-5'>Session Stats:</p>
            <hr className='mt-0' />
            <Row>
              <Col md={3}>
                <StatCard title="Total Sessions" value={stats?.allSessions} />
              </Col>
              <Col md={3}>
                <StatCard title="Scheduled Sessions" value={stats?.scheduledSessions} />
              </Col>
              <Col md={3}>
                <StatCard title="Rescheduled Sessions" value={stats?.rescheduledSessions} />
              </Col>
              <Col md={3}>
                <StatCard title="Cancelled Sessions" value={stats?.cancelledSessions} />
              </Col>
              <Col md={3}>
                <StatCard title="Google Synced Sessions" value={stats?.googleSyncedSessions} />
              </Col>
              <Col md={3}>
                <StatCard title="Max Session Month" value={stats?.sessionMonthData?.maxCount + " (" + stats?.sessionMonthData?.maxMonth + ")"} />
              </Col>
              <Col md={3}>
                <StatCard title="Min Session Month" value={stats?.sessionMonthData?.minCount + " (" + stats?.sessionMonthData?.minMonth + ")"} />
              </Col>
            </Row>
          </Card.Text>

          <Card.Text>
            <p className='mb-0 fs-5'>Payments Stats:</p>
            <hr className='mt-0' />
            <Row>
              <Col md={3}>
                <StatCard title="Total Collection" value={stats?.totalCollection} />
              </Col>
              <Col md={3}>
                <StatCard title="Total Payments" value={stats?.totalPayments} />
              </Col>
              <Col md={3}>
                <StatCard title="Total Completed Payment" value={stats?.totalCompletedPayment} />
              </Col>
              <Col md={3}>
                <StatCard title="Total Offline Payments" value={stats?.totalOfflinePayments} />
              </Col>
              <Col md={3}>
                <StatCard title="Total Failed Payments" value={stats?.totalFailedPayments} />
              </Col>
              <Col md={3}>
                <StatCard title="Total Cancelled Payments" value={stats?.totalCancelledPayments} />
              </Col>
              <Col md={3}>
                <StatCard title="Total Pending Payments" value={stats?.totalPendingPayments} />
              </Col>
            </Row>
            <Row>
              <Col md={3}>
                <StatCard title="Max Payment Month" value={stats?.paymentMonthData?.maxCount + " (" + stats?.paymentMonthData?.maxMonth + ")"} />
              </Col>
              <Col md={3}>
                <StatCard title="Min Payment Month" value={stats?.paymentMonthData?.minCount + " (" + stats?.paymentMonthData?.minMonth + ")"} />
              </Col>
            </Row>
          </Card.Text>

        </Card.Body>
      </Card>

      <ClientListModal 
        show={show}
        handleClose={() => setShow(false)}
        data={stats?.clientIds || []}
      />
    </>
  )
};

function StatCard({ title, value }: any) {
  return (
    <>
      <Card className='mb-2'>
        <Card.Body>
          <Card.Title>{value || 0}</Card.Title>
          <Card.Subtitle className="mb-2 text-muted">{title}</Card.Subtitle>
        </Card.Body>
      </Card>
    </>
  )
}