import { useNavigate } from "react-router-dom";
import { FaTrash, FaEdit } from "react-icons/fa";
import { useState } from "react";
import ConfirmationModal from "../common/ConfirmationModal";

interface Blog {
  _id: string;
  blogName: string;
  writerName: string;
  publishDate: string;
  handle: string;
}

interface BlogTableProps {
  blogs: Blog[];
  deleteBlogHandler: (id: string) => void;
}

export default function BlogTable({ blogs, deleteBlogHandler }: BlogTableProps) {
  const navigate = useNavigate();
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    blogId: '',
    blogName: '',
    loading: false
  });

  const columns = [
    { key: "blogName", header: "Blog Name" },
    { key: "writerName", header: "Author" },
    { key: "publishDate", header: "Publish Date" },
  ];

  const handleDeleteClick = (blog: Blog) => {
    setDeleteModal({
      isOpen: true,
      blogId: blog._id || blog.id,
      blogName: blog.blogName,
      loading: false
    });
  };

  const handleDeleteConfirm = async () => {
    setDeleteModal(prev => ({ ...prev, loading: true }));
    try {
      await deleteBlogHandler(deleteModal.blogId);
      setDeleteModal({
        isOpen: false,
        blogId: '',
        blogName: '',
        loading: false
      });
    } catch (error) {
      setDeleteModal(prev => ({ ...prev, loading: false }));
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({
      isOpen: false,
      blogId: '',
      blogName: '',
      loading: false
    });
  };

  return (
    <div className="rounded-lg">
      {/* Mobile card view */}
      <div className="md:hidden">
        {blogs.map((blog) => (
          <div
            key={blog._id}
            className="flex flex-col bg-white rounded-xl mb-4 shadow-md"
          >
            {columns.map((column) => (
              <div
                key={`${blog._id}-${column.key}-mobile`}
                className="flex justify-between items-center gap-3 px-4 py-3"
              >
                <span className="font-medium text-gray-700 whitespace-normal break-words text-left max-w-[60%]">
                  {column.header}:
                </span>
                <span className="text-gray-600 whitespace-normal break-words text-right max-w-[60%]">
                  {blog[column.key as keyof Blog]}
                </span>
              </div>
            ))}
            <div className="flex justify-end gap-4 px-4 py-3 border-t border-gray-100">
              <button
                onClick={() => navigate(`/blogs/edit/${blog.handle}`)}
                className="text-blue-600 font-medium flex items-center gap-1"
              >
                <FaEdit size={14} />
                Edit
              </button>
              <button
                onClick={() => handleDeleteClick(blog)}
                className="text-red-600 flex items-center gap-1"
              >
                <FaTrash size={14} />
                Delete
              </button>
            </div>
          </div>
        ))}
        {blogs.length === 0 && (
          <div className="px-6 py-4 text-center text-gray-500 bg-white rounded-xl mb-4 shadow-md">
            No blogs available
          </div>
        )}
      </div>
      
      {/* Desktop table view */}
      <div className="hidden md:block">
        <table className="min-w-full divide-y divide-gray-200 bg-white border border-gray-300 shadow-xl">
          <thead className="bg-gray-100">
            <tr>
              {columns.map((col) => (
                <th
                  key={col.key}
                  className="px-6 py-3 text-sm font-medium text-gray-700 text-left font-semibold"
                >
                  {col.header}
                </th>
              ))}
              <th className="px-6 py-3 text-sm font-medium text-gray-700 text-left font-semibold">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {blogs.map((blog) => (
              <tr
                key={blog._id}
                className="cursor-pointer text-sm text-gray-600 md:table-row md:shadow-none"
              >
                {columns.map((col) => (
                  <td
                    key={`${blog._id}-${col.key}-desktop`}
                    className="px-6 py-4 text-sm text-gray-600 text-left"
                  >
                    {blog[col.key as keyof Blog]}
                  </td>
                ))}
                <td className="px-6 py-4 text-sm text-left gap-3 flex">
                  <button
                    onClick={() => navigate(`/blogs/view/${blog._id || blog.id}`)}
                    className="font-medium text-blue-600 hover:text-blue-700 flex items-center gap-1"
                  >
                    <FaEdit size={14} />
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteClick(blog)}
                    className="font-medium ml-4 text-red-600 hover:text-red-700 flex items-center gap-1"
                  >
                    <FaTrash size={14} />
                    Delete
                  </button>
                </td>
              </tr>
            ))}
            {blogs.length === 0 && (
              <tr>
                <td
                  colSpan={columns.length + 1}
                  className="px-6 py-4 text-center text-gray-500"
                >
                  No blogs available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Blog Post"
        message={`Are you sure you want to delete "${deleteModal.blogName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        loading={deleteModal.loading}
      />
    </div>
  );
}
