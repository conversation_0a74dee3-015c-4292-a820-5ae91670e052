import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class JournalService {
  // Get journal section data
  static async getJournalSection() {
    console.log("Fetching journal section data");
    const response = await makeRequest("/homepage-sections?section=journal", RequestMethods.GET);
    console.log("Journal section response:", response);
    return response;
  }

  // Update journal section with selected blogs
  static async updateJournalSection(journalData: any) {
    console.log("Updating journal section:", journalData);
    const response = await makeRequest("/homepage-sections/journal", RequestMethods.POST, journalData);
    console.log("Update journal section response:", response);
    return response;
  }

  // Get all public blogs for journal selection
  static async getAllPublicBlogs() {
    console.log("Fetching all public blogs for journal selection");
    const response = await makeRequest("/public/blogs", RequestMethods.GET);
    console.log("Public blogs response:", response);
    return response;
  }

  // Alternative endpoint - get all blogs (fallback)
  static async getAllBlogsForSelection() {
    console.log("Fetching all blogs for journal selection (fallback)");
    const response = await makeRequest("/blogs", RequestMethods.GET);
    console.log("All blogs response:", response);
    return response;
  }
}
