import { Dropdown, Table } from "react-bootstrap"
import { FaCash<PERSON><PERSON><PERSON>, FaEllipsis<PERSON>, <PERSON>a<PERSON><PERSON>, FaFileInvoiceDollar, FaSync } from "react-icons/fa";
import CustomToggle from "../Menu/CustomMenu";
import { useNavigate } from "react-router-dom";
import PayoutUpdateModal from "../Modals/PayoutUpdateModal";
import React, { useEffect } from "react";
import StatusLabel from "../Labels/status.label";
import moment from "moment";
import MakeTransactionModal from "../Modals/MakeTransaction.modal";

interface IPayoutTable {
    tableData: any,
    reload: any
}

export default function PayoutTable({ tableData, reload }: IPayoutTable) {

    const navigate = useNavigate();


    const [showUpdateModal, setShowUpdateModal] = React.useState<any>(undefined);
    const [makeTherapistPayout, setMakeTherapistPayout] = React.useState<any>(undefined);

    useEffect(() => {
       reload();
    },[showUpdateModal])

    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead style={{ textAlign: "center" }}>
                        <tr>
                            <th>Sr.No</th>
                            <th> Name</th>
                            <th>Billed</th>
                            <th>Comm.</th>
                            <th>TBT</th>
                            <th>Invoices</th>
                            <th>Deductions</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>T. Date</th>
                            <th>Mode</th>
                        </tr>
                    </thead>
                    <tbody style={{ textAlign: "center" }}>
                        {tableData && tableData.length > 0 ? tableData.map((data: any, index: number) => {
                            return (
                                <tr className="fs-small">
                                    <td className="text-muted">{index + 1}</td>
                                    <td className="text-primary">{data?.therapistId?.name || "--"}</td>
                                
                                    <td className={`fw-bold ${data.totalAmount > 0 ? "text-success": "text-danger"}`}> {data?.totalAmount || "--"}</td>
                                    <td>{data?.commission}</td>
                                    <td className={`fw-bold ${data.totalAmount > 0 ? "text-success": "text-danger"}`}> {data?.amountToBePaid}</td>
                                    <td>{data?.invoices?.length}</td>
                                    <td>{data?.deductions?.length}</td>
                                    <td><StatusLabel status={data?.payoutStatus || "--"}/></td>
                                    <td className="fs-small text-muted">{moment(data.createdAt).format("DD MM YY")}</td>
                                    <td className="fs-small fw-bold text-success">{data.transferDate ? moment(data.transferDate).format("DD MM YY"): "--"}</td>
                                    <td>{data?.payoutMode || "--"}</td>
                                    <td>
                                        <Dropdown>
                                            <Dropdown.Toggle
                                                as={CustomToggle}
                                                id="dropdown-custom-components"
                                            >
                                                <FaEllipsisV className="cursor-pointer" />
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu>
                                                <Dropdown.Item
                                                    onClick={() => setShowUpdateModal(data._id)}
                                                >
                                                    <FaCashRegister className="text-muted me-2" />
                                                    <span className="fw-bold text-secondary fs-12">
                                                        Update Manually
                                                    </span>
                                                </Dropdown.Item>

                                                <Dropdown.Item
                                                    onClick={() => setMakeTherapistPayout(data._id)}
                                                >
                                                    <FaFileInvoiceDollar className="text-muted me-2" />
                                                    <span className="fw-bold text-secondary fs-12">
                                                        Transfer Using RZPX UPI
                                                    </span>
                                                </Dropdown.Item>
                                            </Dropdown.Menu>
                                        </Dropdown>
                                    </td>
                                </tr>
                            )
                        }) : <tr>
                            <td colSpan={10} className="fw-bold text-muted">
                                No Transactions to show create a payout first
                            </td>
                        </tr>
                        }
                    </tbody>
                </Table>
            </div>
            <PayoutUpdateModal
                show={showUpdateModal}
                handleClose={() => setShowUpdateModal(undefined)}

            />

            <MakeTransactionModal
                show={makeTherapistPayout}
                handleClose={() => setMakeTherapistPayout(undefined)}
            />
        </div >
    )
}