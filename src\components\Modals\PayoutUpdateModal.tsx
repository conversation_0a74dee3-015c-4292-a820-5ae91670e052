import React, { useEffect, useState } from 'react'
import { Button, Form, Modal, ModalBody } from 'react-bootstrap'
import Select from 'react-select'
import moment from 'moment'
import ReactDatePicker from 'react-datepicker'
import { PaymentService } from '../../services/payment.service'
import toast from 'react-hot-toast'

interface IPayoutModal {
    show: boolean,
    handleClose: any,
}


const PayoutUpdateModal = (props: IPayoutModal) => {

    enum PayoutStatus {
        PENDING = "PENDING",
        PAID = "PAID",
        CANCELLED = "CANCELLED"
    }

    const payoutModes = [
        {
            label: "Razorpayx UPI",
            value: "razorpayx_upi"
        },
        {
            label: "Manual",
            value: "MANUAL"
        },
        {
            label: "Cash",
            value: "CASH"
        },
        {
            label: "Bank Transfer",
            value: "BANKTRANSFER"
        },
       
        {
            label: "UPI Direct Transfer",
            value: "UPI"
        }
    ]


    const [payoutStatus, setPayoutStatus] = useState<any>({
        transferDate: new Date(),
    });

    const getPayoutById = async () => {
        await PaymentService.getPayoutbyId(props.show).then((res) => {
            if (res.status === 200) {
                setPayoutStatus(res.data.payout)
            }
        }).catch(e => {
            console.log(e)
            props.handleClose()
        })
    }

    const updatePayout = async() => {
        const payload = {
                "payoutStatus": payoutStatus.payoutStatus,
                "transferDate": payoutStatus.transferDate,
                "payoutMode": payoutStatus.payoutMode,
                "refId": payoutStatus.refId
        }
        await PaymentService.updatePayout(props.show, payload).then((res) => {
            if(res.status === 200){
                props.handleClose()
            }
        }).catch(e => {
            console.log(e)
            toast.error("Something went wrong")
        })
    }

    useEffect(() => {
        if (props.show) {
            getPayoutById()
        }
    }, [props.show])
    return (
        <Modal
            show={props.show ? true : false}
            onHide={() => { props.handleClose() }}

        >
            <Modal.Header closeButton>
                <Modal.Title>Update Transfers</Modal.Title>
            </Modal.Header>
            <ModalBody>

                <Form.Group className="mb-3">
                    <Form.Label className="text-muted">Payout Status</Form.Label>
                    <Select options={Object.values(PayoutStatus).map((data) => {
                        return { value: data, label: data }
                    })} onChange={(e: any) => setPayoutStatus({ ...payoutStatus, payoutStatus: e.value })}
                    value={{
                        value: payoutStatus.payoutStatus,
                        label: payoutStatus.payoutStatus
                    }}

                    />
                </Form.Group>
                <Form.Group className="mb-3">
                    <Form.Label className="text-muted">Payout Mode</Form.Label>
                    <Select options={payoutModes} 
                    onChange={(e: any) => setPayoutStatus({ ...payoutStatus, payoutMode: e.value })} 
                    value={payoutModes.find((e) => e.value == payoutStatus.payoutMode)} />

                </Form.Group>
                <Form.Group className="mb-3">
                    <Form.Label className="text-muted">Transferred Date and Time</Form.Label>
                    <div>
                        <ReactDatePicker timeIntervals={1} showTimeSelect className="form-control" selected={payoutStatus.transferDate} onChange={(date: any) => setPayoutStatus({ ...payoutStatus, transferDate: date })} dateFormat="dd/MM/yyyy hh:mm a" />
                    </div>
                </Form.Group>
                <Form.Group className="mb-3">
                    <Form.Label className="text-muted">Reference Id</Form.Label>
                    <Form.Control type="text" placeholder="Reference Id" value={payoutStatus.refId} onChange={(e: any) => setPayoutStatus({ ...payoutStatus, refId: e.target.value })} />
                </Form.Group>
                <Button onClick={updatePayout} >
                    Update Payout
                </Button>
            </ModalBody>
        </Modal>
    )
}

export default PayoutUpdateModal