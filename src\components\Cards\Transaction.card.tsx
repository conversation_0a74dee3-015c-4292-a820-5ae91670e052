import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Tab, Table, Tabs } from "react-bootstrap";
import { useParams } from "react-router-dom";
import { AdminServices } from "../../services/admin.service";
import toast from "react-hot-toast";
import moment from "moment";
import { FaEye } from "react-icons/fa";

export default function TransactionCard() {

    const { id }: any = useParams();
    const [loading, setLoading] = useState<boolean>(false);

    const [transactions, setTransactions] = useState<any[]>([]);
    const [subscriptions, setSubscriptions] = useState<any[]>([]);
    const [therapist, setTherapist] = useState<any>()

    useEffect(() => {
        if (id) {
          fetchTherapistById();
          getAllTransactions(); 
        }
      }, [id]);
      
      const fetchTherapistById = async () => {
        setLoading(true);
        try {
          const res = await AdminServices.getTherapistById(id);
          if (res.status === 200) {
            // console.log("Therapist Data:", res.data);
            setTherapist(res.data);
            // Do something with the data, like setting state
          }
        } catch (err: any) {
          console.error(err);
          toast.error(err.response?.data || "Failed to fetch therapist data");
        } finally {
          setLoading(false);
        }
      };
      

    

    const getAllTransactions = async () => {
        await AdminServices.getAllSubTransactions(id).then((res) => {
            if (res.status === 200) {
                setTransactions(res.data);
            }
        }).catch(err => {
            console.log(err);
            toast.error(err.response.data);
        })
    }

    const refreshTransaction = async (id: any) => {
        setLoading(true);
        await AdminServices.refreshTransaction(id).then((res) => {
            if (res.status === 200) {
                getAllTransactions()
            }
        }).catch(err => {
            console.log(err);
            toast.error(err.response.data);
        }).finally(() => {
            setLoading(false);
        })
        setLoading(false);
    }

    const getAllSubscriptions = async () => {
        await AdminServices.getAllActiveSubscriptions(id).then((res) => {
            if (res.status === 200) {
                setSubscriptions(res.data.data.subscriptions);
            }
        }).catch(err => {
            console.log(err);
            toast.error(err.response.data);
        })
    }

    useEffect(() => {
        if (id) {
            getAllTransactions();
            getAllSubscriptions();
        }
    }, [id])

    return (
        <div>
            <Tabs
                defaultActiveKey="transactions"
                id="justify-tab-example"
                className="mb-3"
                justify
            >
          <Tab eventKey="transactions" title="Transactions">
            <div style={{ overflowX: 'auto', paddingBottom: '1rem' }}>
                <Table
                striped
                hover
                size="sm"
                style={{ minWidth: '1800px', borderCollapse: 'separate', borderSpacing: '0 8px' }}
                >
                <thead>
                    <tr>
                    <th style={{ paddingRight: '20px' }}>S/N</th>
                    <th style={{ paddingRight: '20px' }}>Date</th>
                    <th style={{ paddingRight: '20px' }}>Invoice No</th>
                    <th style={{ paddingRight: '20px' }}>Subscription Name</th>
                    <th style={{ paddingRight: '20px' }}>Invoice Value</th>
                    <th style={{ paddingRight: '20px' }}>Status</th>
                    {therapist?.address?.state === 'Delhi' ? (
                        <th style={{ paddingRight: '20px' }}>IGST (18%)</th>
                    ) : (
                        <>
                        <th style={{ paddingRight: '20px' }}>SGST (9%)</th>
                        <th style={{ paddingRight: '20px' }}>CGST (9%)</th>
                        </>
                    )}
                    <th style={{ paddingRight: '20px' }}>Razorpay Fee (2%)</th>
                    <th style={{ paddingRight: '20px' }}>Total (With Tax & Fee)</th>
                    <th style={{ paddingRight: '20px' }}>Amount Received</th>
                    </tr>
                </thead>
                <tbody>
                    {transactions && transactions.length > 0 ? (
                    transactions.map((transaction: any, index: number) => {
                        const total = transaction?.subscriptionId?.price || 0;
                        const gstAmount = total * 0.18;
                        const razorpayFee = total * 0.02;
                        const amountReceived = total - razorpayFee;

                        return (
                        <tr key={transaction._id}>
                            <td style={{ paddingRight: '20px' }}>{index + 1}</td>
                            <td style={{ paddingRight: '20px' }}>
                            {moment(transaction.createdAt).format('DD-MM-YY hh:mm:ss a')}
                            </td>
                            <td style={{ paddingRight: '20px' }}>
                            {transaction?.invoiceNumber
                                ? `${transaction.invoiceNumber}/${moment(transaction.createdAt).format('MM-YYYY')}/${therapist?.identifier}`
                                : '--'}
                            </td>
                            <td style={{ paddingRight: '20px' }}>
                            {transaction.subscriptionId?.name || '--'}
                            </td>
                            <td style={{ paddingRight: '20px' }}>
                            ₹{Number(transaction.amount).toFixed(2)}
                            </td>
                            <td style={{ paddingRight: '20px' }}>{transaction.paymentStatus}</td>

                            {therapist?.address?.state === 'Delhi' ? (
                            <td style={{ paddingRight: '20px' }}>₹{gstAmount.toFixed(2)}</td>
                            ) : (
                            <>
                                <td style={{ paddingRight: '20px' }}>₹{(gstAmount / 2).toFixed(2)}</td>
                                <td style={{ paddingRight: '20px' }}>₹{(gstAmount / 2).toFixed(2)}</td>
                            </>
                            )}

                            <td style={{ paddingRight: '20px' }}>₹{razorpayFee.toFixed(2)}</td>
                            <td style={{ paddingRight: '20px' }}>
                            <strong>₹{total.toFixed(2)}</strong>
                            </td>
                            <td style={{ paddingRight: '20px' }}>
                            <strong>
                                {transaction?.invoiceNumber ? `₹${amountReceived.toFixed(2)}` : '--'}
                            </strong>
                            </td>
                        </tr>
                        );
                    })
                    ) : (
                    <tr>
                        <td colSpan={13}>No Transactions</td>
                    </tr>
                    )}
                </tbody>
                </Table>
            </div>
            </Tab>


                <Tab eventKey="subscriptions" title="Subscriptions">
                    <Table responsive striped hover size="sm">
                        <thead>
                            <tr>
                                <th>
                                    S/N
                                </th>
                                <th>
                                    Invoice No
                                </th>
                                <th>
                                    Subscription Name
                                </th>
                                <th>
                                    Valid From
                                </th>
                                <th>
                                    Valid Till
                                </th>
                                <th>
                                    Remaining Days
                                </th>
                                <th>
                                    Amount
                                </th>
                                <th>
                                    Payment Date
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {subscriptions && subscriptions.length > 0 ?
                                subscriptions.map((subscription: any, index: number) => {
                                    return (
                                        <tr key={subscription._id}>
                                            <td>{index + 1}</td>
                                            <td>
                                            {subscription?.subscriptionTransactionId?.invoiceNumber
                                                ? `${subscription?.subscriptionTransactionId?.invoiceNumber}/${moment(subscription?.subscriptionTransactionId?.paymentDate).format("MM-YYYY")}/${therapist?.identifier}`
                                                : "--"}
                                            </td>
                                            <td>{subscription.subscriptionId?.name}</td>
                                            <td>{moment(subscription.validFrom).format("DD/MM/YY")}</td>
                                            <td>{moment(subscription.validTill).format("DD/MM/YY")}</td>
                                            <td>
                                            {moment(subscription.validTill).diff(moment(), "days") >= 0
                                                ? moment(subscription.validTill).diff(moment(), "days")
                                                : 0}
                                            </td>
                                            <td>{(subscription.subscriptionTransactionId?.amountReceived)}</td>
                                            <td>{moment(subscription?.subscriptionTransactionId?.paymentDate).format("DD-MM-YY hh:mm:ss a")}</td>
                                        </tr>
                                    )
                                }) : <tr><td colSpan={8}>No Subscriptions</td></tr>}
                        </tbody>
                    </Table>
                </Tab>
            </Tabs>
        </div>
    )
}