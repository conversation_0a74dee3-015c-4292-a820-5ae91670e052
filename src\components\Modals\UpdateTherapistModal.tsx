import { useEffect, useState } from 'react';
import { Form } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';
import { TherapistService } from '../../services/therapist.service';


interface IModalProps {
    handleClose: () => void;
    id: string;
}
interface IData {
    name?: string;
    email?: string;
}

interface IBankDetails {
    bankAccountNo?: string;
    ifscCode?: string;
    branch?: string;
    bankName?: string;
    accountHolderName?: string;
}
function UpdateTherapistModal({ handleClose, id }: IModalProps) {

    const [updateData, setUpdateData] = useState<IData>({ name: '', email: '' });
    const [bankData, setBankData] = useState<IBankDetails>({ bankAccountNo: '', ifscCode: '', branch: '', bankName: '', accountHolderName: '' });

    const getTherapistDetails = async () => {
        id !== "" && await TherapistService.getTherapistById(id)
            .then((res) => {
                if (res.status === 200) {
                    setUpdateData(res.data);
                    setBankData(res.data.bankDetails);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    };

    useEffect(() => {
        getTherapistDetails();
    }, [id]);

    const handleSave = async () => {
        await TherapistService.updateBankDetails(id, bankData)
            .then((res) => {
                if (res.status === 200) {
                    console.log(res.data);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }
    return (
        <>
            <Modal show={id !== ""} onHide={handleClose}>
                <Modal.Header closeButton>
                    <Modal.Title>Update Therapist</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form >
                        <Form.Group controlId="formName">
                            <Form.Label>Name</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter your name"
                                name="name"
                                value={updateData?.name}
                                onChange={(e) => (setUpdateData({ ...updateData, name: e.target.value }))}
                            />
                        </Form.Group>
                        <Form.Group controlId="formEmail">
                            <Form.Label>Email</Form.Label>
                            <Form.Control
                                type="email"
                                placeholder="Enter your email"
                                name="email"
                                value={updateData?.email}
                                onChange={(e) => { setUpdateData({ ...updateData, email: e.target.value }) }}
                            />
                        </Form.Group>

                        <Form.Group controlId="formBankAccountNo">
                            <Form.Label>Bank Account No</Form.Label>
                            <Form.Control

                                type="text"
                                placeholder="Enter your Bank Account No"
                                name="bankAccountNo"
                                value={bankData?.bankAccountNo ? bankData.bankAccountNo : ""}
                                onChange={(e) => { setBankData({ ...bankData, bankAccountNo: e.target.value }) }}
                            />
                        </Form.Group>
                        <Form.Group controlId="formIfscCode">
                            <Form.Label>Ifsc Code</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter your Ifsc Code"
                                name="ifscCode"
                                value={bankData?.ifscCode}
                                onChange={(e) => { setBankData({ ...bankData, ifscCode: e.target.value }) }}
                            />
                        </Form.Group>
                        <Form.Group controlId="formBranch">
                            <Form.Label>Branch</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter your Branch"
                                name="branch"
                                value={bankData?.branch}
                                onChange={(e) => { setBankData({ ...bankData, branch: e.target.value }) }}
                            />
                        </Form.Group>
                        <Form.Group controlId="formBankName">
                            <Form.Label>Bank Name</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter your Bank Name"
                                name="bankName"
                                value={bankData?.bankName}
                                onChange={(e) => { setBankData({ ...bankData, bankName: e.target.value }) }}
                            />
                        </Form.Group>
                        <Form.Group controlId="formAccountHolderName">
                            <Form.Label>Account Holder Name</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter your Account Holder Name"
                                name="accountHolderName"
                                value={bankData?.accountHolderName}
                                onChange={(e) => { setBankData({ ...bankData, accountHolderName: e.target.value }) }}
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                    <Button variant="primary" onClick={handleSave}>
                        Save Changes
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
}

export default UpdateTherapistModal;