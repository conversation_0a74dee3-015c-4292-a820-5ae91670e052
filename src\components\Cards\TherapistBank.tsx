import { useState } from "react";
import { <PERSON><PERSON>, Card, Form } from "react-bootstrap";
import { FiEdit } from "react-icons/fi";

interface ITherapistBankDetails {
    data: any;
    reloadData: any;
    onUpdate: any;
    onSave: any;
}

export default function TherapistBankDetails(props: ITherapistBankDetails) {

    const [edit, setEdit] = useState<boolean>(false);

    const handleDetailsChange = (e: any) => {
        const bankDetails = props.data.bankDetails;
        bankDetails[e.target.name] = e.target.value;
        props.onUpdate({ ...props.data, bankDetails: bankDetails });
    }

    const handleCheckChange = (e: any) => {
        const bankDetails = props.data.bankDetails;
        bankDetails[e.target.name] = e.target.checked;
        props.onUpdate({ ...props.data, bankDetails: bankDetails });
    }

    const handleSettingCheckChange = (e: any) => {
        const settings = props.data.settings;
        settings[e.target.name] = e.target.checked;
        props.onUpdate({ ...props.data, settings: settings });
    }


    const handleCheckBaseChange = (e: any) => {
        props.onUpdate({ ...props.data, [e.target.name]: e.target.checked });
    }

    const onSaveClick = async() => {
        const save = await props.onSave();
        if(save){
          setEdit(false);
          props.reloadData();
        }
      }

    return (
        <Card className="mt-3">
            <Card.Body>
                <Card.Text className="mt-3">
                    <div>
                        <b className="fs-4 p-0">Bank Details:</b>
                    </div>
                    <div className="d-flex justify-content-end">
                        {edit ?
                            <Button className="px-5" variant="success" size="sm" onClick={onSaveClick}>Save</Button> :
                            <FiEdit style={{ fontSize: 20 }} onClick={() => setEdit(true)} />
                        }
                    </div>
                    <hr />
                    <div>
                        <div className="d-flex justify-content-between mb-3 mt-3">
                            <b className="fw-bold"> Bank Account No:</b>
                            <div style={{ width: "80%" }}>
                                <Form.Control value={props.data?.bankDetails?.bankAccountNo} name="bankAccountNo" onChange={handleDetailsChange} disabled={!edit} />
                            </div>
                        </div>
                        <div className="d-flex justify-content-between mb-3">
                            <b className="fw-bold"> IFSC Code:</b>
                            <div style={{ width: "80%" }}>
                                <Form.Control value={props.data?.bankDetails?.ifscCode} name="ifscCode" onChange={handleDetailsChange} disabled={!edit} />
                            </div>
                        </div>
                        <div className="d-flex justify-content-between mb-3">
                            <b className="fw-bold">Bank Name:</b>
                            <div style={{ width: "80%" }}>
                                <Form.Control value={props.data?.bankDetails?.bankName} name="bankName" onChange={handleDetailsChange} disabled={!edit} />
                            </div>
                        </div>
                        <div className="d-flex justify-content-between mb-3">
                            <b className="fw-bold"> Branch:</b>
                            <div style={{ width: "80%" }}>
                                <Form.Control value={props.data?.bankDetails?.branch} name="branch" onChange={handleDetailsChange} disabled={!edit} />
                            </div>
                        </div>
                        <div className="d-flex justify-content-between mb-3">
                            <b className="fw-bold">UPI Id:</b>
                            <div style={{ width: "80%" }}>
                                <Form.Control value={props.data?.bankDetails?.upiId} name="upiId" onChange={handleDetailsChange} disabled={!edit} />
                            </div>
                        </div>
                        <div className="d-flex mb-3">
                            <b> UPI Approved:</b>
                            <Form.Switch className="ms-3" checked={props.data?.bankDetails?.upiApprove} name="upiApprove" onChange={handleCheckChange} disabled={!edit} />
                        </div>
                    </div>
                </Card.Text>

                <div className="mt-3">
                    <Card.Text>
                        <div>
                            <b>Settings:</b>
                        </div>
                        <hr />
                        <div className="mt-2 d-flex justify-content-between align-items-center">
                            <div className="d-flex mb-3">
                                <b> Email Notifications:</b>
                                <Form.Switch className="ms-3" name="emailNotification" checked={props.data?.settings?.emailNotification} onChange={handleSettingCheckChange} disabled={!edit} />
                            </div>
                            <div className="d-flex mb-3">
                                <b> Email on Session Confirmation:</b>
                                <Form.Switch className="ms-3" name="emailOnSessionConfirmation" checked={props.data?.settings?.emailOnSessionConfirmation} onChange={handleSettingCheckChange} disabled={!edit} />
                            </div>
                            <div className="d-flex mb-3">
                                <b>Weekly Reports Notifications:</b>
                                <Form.Switch className="ms-3" name="weeklyReportsNotification" checked={props.data?.settings?.weeklyReportsNotification} onChange={handleSettingCheckChange} disabled={!edit} />
                            </div>
                            <div className="d-flex mb-3">
                                <b>Verified:</b>
                                <Form.Switch className="ms-3" name="isVerified" checked={props.data?.isVerified} onChange={handleCheckBaseChange} disabled={!edit} />
                            </div>
                            <div className="d-flex mb-3">
                                <b>Deleted:</b>
                                <Form.Switch className="ms-3" name="isDeleted" checked={props.data?.isDeleted} onChange={handleCheckBaseChange} disabled={!edit} />
                            </div>
                        </div>
                    </Card.Text>
                </div>

            </Card.Body>
        </Card>
    )
}