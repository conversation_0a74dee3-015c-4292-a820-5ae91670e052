import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class BlogService {
  // Get all blogs with pagination
  static async getAllBlogs(page: number = 1, limit: number = 5) {
    // Try without pagination first to see if that's the issue
    const endpoint = `/blogs?page=${page}&limit=${limit}`;
    console.log("Fetching blogs from:", endpoint);

    const response = await makeRequest(endpoint, RequestMethods.GET);
    console.log("API Response:", response);

    return response;
  }

  // Get all blogs without pagination (for testing)
  static async getAllBlogsNoPagination() {
    console.log("Fetching all blogs without pagination");
    const response = await makeRequest("/blogs", RequestMethods.GET);
    console.log("API Response (no pagination):", response);
    return response;
  }

  // Get single blog by handle
  static async getBlogByHandle(handle: string) {
    console.log("Fetching blog by handle:", handle);
    const response = await makeRequest(`/blogs/${handle}`, RequestMethods.GET);
    console.log("API Response (by handle):", response);
    return response;
  }

  // Get single blog by ID
  static async getBlogById(id: string) {
    console.log("Fetching blog by ID:", id);
    const response = await makeRequest(`/blogs/${id}`, RequestMethods.GET);
    console.log("API Response (by ID):", response);
    return response;
  }

  // Alternative endpoint for getting blog by ID
  static async getBlogByIdAlt(id: string) {
    console.log("Fetching blog by ID (alternative endpoint):", id);
    const response = await makeRequest(`/blog/${id}`, RequestMethods.GET);
    console.log("API Response (by ID alt):", response);
    return response;
  }

  // Create new blog
  static async createBlog(blogData: any) {
    return await makeRequest("/blogs", RequestMethods.POST, blogData);
  }

  // Update existing blog
  static async updateBlog(handle: string, blogData: any) {
    return await makeRequest(`/blogs/${handle}`, RequestMethods.PUT, blogData);
  }

  // Delete blog
  static async deleteBlog(handle: string) {
    return await makeRequest(`/blogs/${handle}`, RequestMethods.DELETE);
  }

  // Upload blog cover image
  static async uploadBlogImage(file: File) {
    console.log("Uploading blog image:", file.name);
    const formData = new FormData();
    formData.append("image", file);

    const response = await makeRequest("/blogs/upload/blog-image", RequestMethods.POST, formData);
    console.log("Blog image upload response:", response);
    return response;
  }

  // Upload writer image
  static async uploadWriterImage(file: File) {
    console.log("Uploading writer image:", file.name);
    const formData = new FormData();
    formData.append("image", file);

    const response = await makeRequest("/blogs/upload/writer-image", RequestMethods.POST, formData);
    console.log("Writer image upload response:", response);
    return response;
  }

  // Delete image
  static async deleteImage(imageUrl: string) {
    console.log("Deleting image:", imageUrl);
    const response = await makeRequest("/blogs/image/delete", RequestMethods.DELETE, { imageUrl });
    console.log("Delete image response:", response);
    return response;
  }

  // Legacy upload file method (keeping for backward compatibility)
  static async uploadFile(file: File) {
    console.log("Using legacy upload for file:", file.name);
    return this.uploadBlogImage(file);
  }
}
