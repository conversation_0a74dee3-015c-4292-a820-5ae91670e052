{"name": "therapist_admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 1337 --host"}, "dependencies": {"@types/react-datepicker": "^4.19.3", "axios": "^1.4.0", "bootstrap": "^5.3.1", "json-to-csv-export": "^2.1.1", "moment": "^2.29.4", "react": "^18.2.0", "react-bootstrap": "^2.8.0", "react-datepicker": "^4.23.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.46.1", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-quill": "^2.0.0", "react-router-dom": "^6.14.2", "react-select": "^5.8.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-google-recaptcha": "^2.1.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.0.2", "vite": "^4.4.5"}}