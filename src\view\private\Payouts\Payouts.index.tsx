import { <PERSON><PERSON>, <PERSON>, Col, Form, Pagination, Row } from "react-bootstrap";
import Select from "react-select";
import { TherapistService } from "../../../services/therapist.service";
import { useEffect, useState } from "react";
import TherapistTable from "../../../components/Table/Therapist.table";
import { PaymentService } from "../../../services/payment.service";
import PendingInvoiceTable from "../../../components/Table/PendingInvoicesTable";
import PendingDeductionsTable from "../../../components/Table/PendingDeductionsTable";
import PayoutTable from "../../../components/Table/Payout.table";
import toast from "react-hot-toast";
import csvDownload from "json-to-csv-export";
import PendingLinksTable from "../../../components/Table/PendingLinks.tables";

export default function Payouts() {
    const [therapist, setTherapist] = useState<any>();
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [show, setShow] = useState<boolean>(false);

    const [payouts, setPayouts] = useState<any>();

    const [invoice, setInvoice] = useState<any>();
    const [selectedInvoiceIds, setSelectedInvoiceIds] = useState<any>([]);
    const [csvData, setCsvData] = useState<any>([]);
    const [deduction, setDeduction] = useState<any>([]);
    const [selectedDeductionIds, setSelectedDeductionIds] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [totalCount, setTotalCount] = useState({
        invoice: 0,
        deduction: 0,
        comission: 10,
        final: 0
    });
    let items: any = [];

    for (let number = 1; number <= pageNumber; number++) {
        items.push(
            <Pagination.Item key={number} active={number === pageNumber}>
                {number}
            </Pagination.Item>
        );
    }

    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(1, 999).then((res) => {
            if (res.status === 200) {
                setTherapist(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }

    const getPendingInvoice = async () => {
        await PaymentService.getPendingInvoices(1, 9999, true, selectTherapist).then((res) => {
            if (res.status === 200) {
                setInvoice(res.data.invoices)
                setSelectedInvoiceIds([]);
                setTotalCount({
                    ...totalCount,
                    invoice: 0
                });
            }
        })
            .catch((err) => {
                console.log(err);
            })
    }

    const getPendinDeduction = async () => {
        await PaymentService.getPendingDeduction(1, 9999, true, selectTherapist).then((res) => {
            if (res.status === 200) {
                // console.log(res.data);
                setDeduction(res.data)
                setSelectedDeductionIds([]);
                setTotalCount({
                    ...totalCount,
                    invoice: 0
                });
            }
        })
            .catch((err) => {
                console.log(err);
            })
    }

    const getPayouts = async () => {
        if (!selectTherapist) return;
        await PaymentService.getPayouts(1, 999999, selectTherapist, undefined).then((res) => {
            if (res.status === 200) {
                setPayouts(res.data.payouts)
                console.log(res.data.payouts, "payouts")
            }
        }).catch(e => {
            console.log(e);
        })
    }

    const handleCreatePayout = async () => {
        if (!selectTherapist) return;
        // console.log(selectTherapist);
        setLoading(true);
        const payload = {
            'commission': totalCount.comission,
            'invoiceIds': invoice.map((item: any) => item._id),
            'deductionIds': deduction.map((item: any) => item._id)
        }
        await PaymentService.createPayout(selectTherapist, payload)
            .then((res) => {
                if (res.status === 200) {
                    toast.success("Payout created successfully");
                    getPayouts();
                }
            })
            .catch((err) => {
                console.log(err);
            })
            .finally(() => {
                setLoading(false);
            })
    };
    useEffect(() => {
        getAllTherapist()
    }, [pageNumber, pageSize])

    useEffect(() => {
        setShow(false);
        if (selectTherapist) {
            getPendingInvoice();
            getPendinDeduction();
            // getCounts();
            setShow(true);
            getPayouts();
        }
    }, [selectTherapist]);

    const getCounts = () => {
        let invoiceTotal = 0;
        let deductionTotal = 0;

        invoice && invoice.forEach((element: any) => {
            invoiceTotal += parseInt(element.invoiceValue);
        });

        deduction && deduction.forEach((element: any) => {
            deductionTotal += parseInt(element.amount);
        });

        const commission = totalCount.comission / 100;
        let finalAmount = (invoiceTotal - deductionTotal) - ((invoiceTotal - deductionTotal) * commission);

        setTotalCount({
            invoice: invoiceTotal,
            deduction: deductionTotal,
            comission: totalCount.comission,
            final: finalAmount
        });
    };

    const getCSVData = async() => {
        await PaymentService.getPendingPayout()
        .then((res) => {
            if (res.status === 200) {
                setCsvData(res.data.csvData);
            }
        })
        .catch((err) => {
            console.log(err);
        })
    }
    const handleDownload = async () => {
        const dataToConvert = {
            data: csvData,
            filename: 'pending-payouts',
            delimiter: ',',
            headers: ['Therapist Name', "Therapist UPI Id", "Amount"]
        }
        csvDownload(dataToConvert);
    }

    useEffect(() => {
        getCSVData();
    }, []);
    useEffect(() => {
        getCounts();
    }, [invoice, deduction, totalCount.comission]);
    return (
        <>
            <Row>
                <div className="d-flex mb-3 justify-content-end">
                    <button className="shadow btn btn-success" onClick={handleDownload}>
                        Download Pending Payouts
                    </button>
                </div>
                <Form.Group className="p-2 mb-3 card shadow border-0">
                    <Form.Label className="text-muted">Select Therapist</Form.Label>
                    <Select
                        options={therapist}
                        onChange={(e: any, action: any) => {
                            if (action.action === 'clear') {
                                setShow(false);
                                setSelectTherapist(null);
                                return;
                            }
                            setSelectTherapist(e.value)
                        }}
                        isClearable
                    />
                </Form.Group>

                {
                    show &&
                    <>
                        <Card className="mt-3 shadow">
                            <Row>
                                <Col md={6}>
                                    <div className="mt-3 fw-bold">Pending Invoices:</div>
                                    <div className="mb-3">
                                        <PendingInvoiceTable tableData={invoice} selectedIds={selectedInvoiceIds} setSelectedIds={setSelectedInvoiceIds} />
                                    </div>
                                </Col>
                                <Col md={6}>
                                    <div className="mt-3 fw-bold">Pending Deductions:</div>
                                    <div className="mb-3">
                                        <PendingDeductionsTable tableData={deduction} selectedIds={selectedDeductionIds} setSelectedIds={setSelectedDeductionIds} />
                                    </div>
                                </Col>
                            </Row>
                        </Card>

                        <Card className="p-2 mt-3 shadow">
                            <Col md={6}>
                                <div className="mt-3 fw-bold">Pending Links:</div>
                                <div className="mb-3">
                                    <PendingLinksTable therapistId={selectTherapist} />
                                </div>
                            </Col>
                        </Card>

                        <Card className="p-2 mt-3 shadow">
                            <Card.Title className="">
                                Summary
                            </Card.Title>
                            <Row>
                                <Col md={6}>
                                    <Form.Label className="text-muted">Total Invoices</Form.Label>
                                    <Form.Control type="text" placeholder="total invoices" disabled value={totalCount.invoice} />
                                </Col>
                                <Col md={6}>
                                    <Form.Label className="text-muted">Total Deductions</Form.Label>
                                    <Form.Control type="text" placeholder="total invoices" disabled value={totalCount.deduction} />
                                </Col>
                            </Row>
                            <Row className="mt-2">
                                <Col md={6}>
                                    <Form.Label className="text-muted">Comission %</Form.Label>
                                    <Form.Control type="number" placeholder="Commision percentage" min={1} max={100}
                                        value={totalCount.comission}
                                        onChange={(e: any) => setTotalCount({
                                            ...totalCount,
                                            comission: e.target.value
                                        })}
                                    />
                                </Col>
                                <Col md={6}>
                                    <Form.Label className="text-muted">Final Amount</Form.Label>
                                    <Form.Control type="text" placeholder="total invoices" disabled value={totalCount.final} />
                                </Col>
                            </Row>
                            <Row className="mt-2">
                                <Col md={6}>
                                    <Button className="w-50" variant="success" size="lg" disabled={loading || totalCount.final <= 0} onClick={handleCreatePayout}>Create Payout</Button>
                                </Col>
                            </Row>
                        </Card>


                        <Card className="mt-3 mb-3 shadow">
                            <Row>
                                <Col>
                                    <div className="mt-3 fw-bold">Previous payouts:</div>
                                    <div>
                                        <PayoutTable tableData={payouts} reload={getPayouts} />
                                    </div>
                                </Col>
                            </Row>
                        </Card>
                    </>
                }
            </Row>
        </>
    )
}