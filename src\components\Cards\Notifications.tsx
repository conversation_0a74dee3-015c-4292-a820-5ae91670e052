import React, { useEffect, useState } from 'react'
import { But<PERSON> } from 'react-bootstrap'
import CreateNotificationModal from '../Modals/CreateNotification.modal'
import NotificationsTable from '../Table/Notifications.table';
import { TherapistService } from '../../services/therapist.service';
import { useParams } from 'react-router-dom';
import toast from 'react-hot-toast';

export default function Notifications() {
  const [show, setShow] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any>([]);
  const params = useParams();
  async function getAllNotification(){
    await TherapistService.getAllNotifications(params.id)
      .then((res) => {
        if(res.status === 200){
          setTableData(res.data?.data?.notifications);
          // console.log(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
  }

  async function handleDelete(id:string){
    await TherapistService.deleteNotificationById(id)
      .then((res) => {
        if(res.status === 200){
          toast.success("Notification Deleted");
          getAllNotification();
        }
      })
      .catch((err) => {
        console.log(err);
        toast.error(err?.response?.data);
      })
  }

  useEffect(() => {
    getAllNotification();
  }, [])

  return (
    <>
      <div className="mt-3 mb-3 bg-light p-4 rounded">
        <div className="d-flex justify-content-between align-items-center">
          <h5 className="text-muted mb-2">
            Notifications
          </h5>
          <Button size="sm" onClick={() => setShow(true)}>
            Send New Notification
          </Button>
        </div>
        <NotificationsTable tableData={tableData} handleDelete={(id:string) => handleDelete(id)} />
      </div>

      <CreateNotificationModal
        show={show}
        handleClose={() => setShow(false)}
        reloadData={getAllNotification}
      />
    </>
  )
}