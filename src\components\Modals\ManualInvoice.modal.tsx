import { useEffect, useState } from "react";
import { Off<PERSON><PERSON>, Form, Button } from "react-bootstrap";
import { TherapistService } from "../../services/therapist.service";
import Select from "react-select"
import { PaymentService } from "../../services/payment.service";
import moment from "moment";
import toast from "react-hot-toast";

interface IManualInvoiceModal {
    show: any,
    handleClose: any
}

export default function ManualInvoiceModal({ show, handleClose }: IManualInvoiceModal) {

    const [therapist, setTherapist] = useState<any>();
    const [clients, setClients] = useState<any>();
    const [schedule, setSchedule] = useState<any>();
    const [recurrenceSchedule, setRecurrenceSchedule] = useState<any>();
    const [pageNumber, setPageNumber] = useState<number>(1)
    const [pageSize, setPageSize] = useState<number>(999)
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [selectClient, setSelectClient] = useState<any>()
    const [selectSchedule, setSelectSchedule] = useState<any>();
    const [selectRecurSchedule, setSelectRecurSchedule] = useState<any>();
    const [isInternational, setIsInternational] = useState<boolean>(false);


    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(pageNumber, pageSize).then((res) => {
            if (res.status === 200) {
                setTherapist(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }

    const getAllClients = async () => {
        await PaymentService.getAllClients(pageNumber, pageSize, selectTherapist).then((res) => {
            if (res.status === 200) {
                setClients(res.data.map((client: any) => {
                    return {
                        label: client?.clientId,
                        value: client._id
                    }
                }))
            }
        })
    }

    const getAllSchedule = async () => {
        await PaymentService.getAllSchedule(pageNumber, pageSize, selectTherapist, selectClient).then((res) => {
            if (res.status === 200) {
                const schedules = res.data.map((schedule: any) => {
                    const { recurrenceDates, ...restSchedule } = schedule;
                    setRecurrenceSchedule(recurrenceDates.map((recur: any) => {
                        return {
                            label: moment(recur.fromDate).format("DD/MM/YY, hh:mm A") + " - " + moment(recur.toDate).format("DD/MM/YY, hh:mm A"),
                            value: recur._id
                        };
                    }));
                    return {
                        label: restSchedule.name + " " + `(${restSchedule.email})`,
                        value: restSchedule._id
                    };
                });
                setSchedule(schedules);
            }
        })
    }

    const createInvoice = async () => {
        const payload: any = {
            therapistId: selectTherapist,
            clientId: selectClient,
            scheduleId: selectSchedule,
            scheduleRecId: selectRecurSchedule,
            isInternational: isInternational
        }
        await PaymentService.createInvoice(payload).then((res) => {
            if (res.status === 200) {
                handleClose();
                toast.success("Invoice generated");
            }
        }).catch(err => {
            toast.error(err.response.data)
        })
    }


    useEffect(() => {
        if (selectTherapist) {
            getAllClients();
        }
    }, [selectTherapist])

    useEffect(() => {
        if (selectClient) {
            getAllSchedule();
        }
    }, [selectClient])



    useEffect(() => {
        getAllTherapist();
    }, [])

    return (
        <Offcanvas show={show} onHide={handleClose} placement="end">
            <Offcanvas.Header closeButton>
                <Offcanvas.Title>Create Invoice</Offcanvas.Title>
            </Offcanvas.Header>
            <Offcanvas.Body className="d-flex flex-column justify-content-between">
                <div>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Therapist</Form.Label>
                        <Select options={therapist} onChange={(e: any) => setSelectTherapist(e.value)} />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Client</Form.Label>
                        <Select options={clients} onChange={(e: any) => setSelectClient(e.value)} />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Schedule</Form.Label>
                        <Select options={schedule} onChange={(e: any) => setSelectSchedule(e.value)} />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Recurrence Schedule</Form.Label>
                        <Select options={recurrenceSchedule} onChange={(e: any) => setSelectRecurSchedule(e.value)} />
                    </Form.Group>
                    <Form.Group className="mb-3 d-flex">
                        <Form.Label className="text-muted">International</Form.Label>
                        <Form.Switch className="ms-3" checked={isInternational} onChange={(e: any) => setIsInternational(e.target.checked)} />
                    </Form.Group>
                </div>
                <div>
                    <Button className="w-100" onClick={createInvoice}>Create</Button>
                </div>
            </Offcanvas.Body>
        </Offcanvas>
    )
}