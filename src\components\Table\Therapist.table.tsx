import React from 'react';
import { Dropdown, OverlayTrigger, Table, Tooltip, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaEllipsisV, FaEye } from 'react-icons/fa';
import CustomToggle from '../Menu/CustomMenu';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle } from 'react-icons/fa';
import { RxCrossCircled } from 'react-icons/rx';

interface ITherapistTable {
    tableData: any[];
    loading: boolean;
}

export default function TherapistTable({ tableData, loading }: ITherapistTable) {
    const navigate = useNavigate();

    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="responsive-table">
                    <thead style={{ textAlign: 'center' }}>
                        <tr>
                            <th>Sr.No</th>
                            <th>Id</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th><PERSON><PERSON> Approved</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody style={{ textAlign: 'center' }}>
                        {
                            !loading && tableData && tableData.length > 0 ? tableData.map((data: any, index: number) => (
                                <tr key={index}>
                                    <td>{index + 1}</td>
                                    <td>{data?.identifier}</td>
                                    <td>{data?.name || '--'}</td>
                                    <td>{data?.email || '--'}</td>
                                    <td style={{ textAlign: 'center' }}>
                                        {data?.bankDetails?.upiApprove ? (
                                            <FaCheckCircle className="text-success" />
                                        ) : (
                                            <RxCrossCircled className="text-danger" />
                                        )}
                                    </td>
                                    <td>
                                        <Dropdown>
                                            <Dropdown.Toggle as={CustomToggle} id="dropdown-custom-components">
                                                <FaEllipsisV className="cursor-pointer" />
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu>
                                                <Dropdown.Item onClick={() => navigate('/therapist/' + data._id)}>
                                                    <FaEye className="text-secondary" />{' '}
                                                    <span className="fw-bold text-secondary fs-12">View</span>
                                                </Dropdown.Item>
                                            </Dropdown.Menu>
                                        </Dropdown>
                                    </td>
                                </tr>
                            )) :
                                loading ? (
                                    <tr>
                                        <td colSpan={6}> <span className='w-100 d-flex align-items-center justify-content-center'>
                                            Loading... <Spinner animation="border" variant="secondary" />
                                        </span>
                                        </td>
                                    </tr>
                                ) :
                                    (
                                        <tr>
                                            <td colSpan={6}>No data found</td>
                                        </tr>
                                    )
                        }
                    </tbody>
                </Table>
            </div>
        </div>
    );
}
