import { Table, Pa<PERSON><PERSON>, <PERSON><PERSON>, Badge } from "react-bootstrap";
import TableTopbar from "./TableTopBar";
import moment from "moment";
import { useState } from "react";
import UpdateTherapistModal from "../Modals/UpdateTherapistModal";
import { useNavigate } from "react-router-dom";


interface ITableIndexProps {
    tableHeaders: string[];
    tableData: any[];
    topBar?: boolean;
};
export default function TableIndex({ tableHeaders, tableData , topBar = true}: ITableIndexProps) {
    const [updateId, setUpdateId] = useState("");
    const navigate = useNavigate();

    let items = [];
    let active = 2;
    

    const handleUpdate = (_id : string) => {   
        setUpdateId(_id);
    }

    for (let number = 1; number <= 5; number++) {
        items.push(
            <Pagination.Item key={number} active={number === active}>
                {number}
            </Pagination.Item>
        );
    }
    const formatTime = (time: string) => {
        return moment(time).format("DD MMMM YYYY HH:mm");
    }
    return (
        <div className="border rounded grid-border mt-2">
            {topBar && <TableTopbar />}
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            <th scope="col">
                                <input
                                    className="form-check-input"
                                    type="checkbox"
                                    value=""
                                ></input>
                            </th>
                            {
                                tableHeaders.map((header, index) => {
                                    return (
                                        <th key={index} scope="col">{header}</th>
                                    )
                                })
                            }
                        </tr>
                    </thead>
                    <tbody>
                        {
                            tableData.length > 0 ?
                                tableData?.map((data :any) => {
                                    return (
                                        <tr>
                                            <td scope="col">
                                                <input
                                                    className="form-check-input"
                                                    type="checkbox"
                                                    value=""
                                                ></input>
                                            </td>
                                            {
                                                tableHeaders.map((header, index) => {
                                                    if(header === "Client" || header === "Therapist"){
                                                        return (
                                                            <td key={index}>
                                                                <div className="contact-name">{ data.name || 'Client Name'}</div>
                                                            </td>
                                                        )
                                                    }
                                                    else if (header === "Name") {
                                                        return (
                                                            <td key={index}>
                                                                <div className="contact-name">{data.name}</div>
                                                            </td>
                                                        )
                                                    }
                                                    else if(header === "Client ID" || header === "Session ID"){
                                                        return (
                                                            <td key={index}>
                                                                <div className="contact-name">{data._id || data.id}</div>
                                                            </td>
                                                        )
                                                    }else if(header === "Date"){
                                                        return (
                                                            <td key={index}>
                                                                <div className="contact-name">{formatTime(data.date)}</div>
                                                            </td>
                                                        )
                                                    }
                                                    else if(header === "Sessions"){
                                                        return (
                                                            <td key={index}>
                                                                <div className="contact-name">{data.sessions || 50}</div>
                                                            </td>
                                                        )
                                                    }
                                                    else if (header === "Payments") {
                                                        return (
                                                            <td key={index}>
                                                                <div className="contact-name">{data.payments || '$100'}</div>
                                                            </td>
                                                        )
                                                    }
                                                    else if (header === "Email") {
                                                        return (<td key={index}>
                                                            <div className="contact-email">{data.email}</div>
                                                        </td>)
                                                    } else if (header === "Last Updated") {
                                                        return (<td key={index}>
                                                            <div className="contact-email">{formatTime(data.updatedAt)}</div>
                                                        </td>)
                                                    } else if (header === "Actions") {
                                                        return (<td key={index}>
                                                            <Button href="#" className="btn btn-sm btn-success " onClick={() => {navigate(`../therapist/${data._id}` , { relative: "path" })}}>Info</Button>
                                                            <Button href="#" className="btn btn-sm btn-secondary ms-2" onClick={() => handleUpdate(data._id)}>Update</Button>
                                                            <Button href="#" className="btn btn-sm btn-danger ms-2">Delete</Button>
                                                        </td>)
                                                    }else if(header === "Status"){
                                                        return (<td key={index}>
                                                            <div className="contact-email">{data.payment} <Badge bg="warning">{data.status}</Badge> </div>
                                                        </td>)
                                                    }
                                                })
                                            }
                                        </tr>
                                    )
                                }) : (<td colSpan={tableHeaders.length} className="text-center">No Data Found</td>)
                        }
                    </tbody>
                </Table>
            </div>
            <div className="px-0 px-md-3">
                <div className="d-flex flex-column flex-md-row justify-content-md-between">
                    <div>
                        <Pagination>
                            <Pagination.Prev />
                            {items}
                            <Pagination.Next />
                        </Pagination>
                    </div>
                    <div>
                        <div className='row'>
                            <div className="col pe-md-0">
                                <span className="small flex-grow-1">Record Per Page </span>
                            </div>
                            <div className="col-auto">
                                <select className="form-select bg-light form-select-sm" aria-label=".form-select-sm example">
                                    <option selected>1</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <UpdateTherapistModal
                handleClose={() => {setUpdateId("")}}
                id={updateId}
            />
        </div>
    )
}