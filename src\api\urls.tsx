const url = {
    login: "/auth/admin/login",
    dahsboard: {
        getStats: "/admin/dashboard/stats",
        getDetailStats: '/admin/dashboard/details'
    },
    downloadFile: "/admin/get/document",
    therapist: {
        getAllTherapist: "/admin/therapist/all",
        getTherapistById: "/therapist/get",
        updateBankDetails: "/admin/updateBankDetails",
        updateTherapistDetails: "/admin/therapist/update",
        getSessions: "/admin/sessions/all",
        getTherapistStats: "/admin/therapist/stats",
        getAllTherapistCount: '/admin/therapist/count/all',
        updateTherapistDocs: '/admin/therapist/doc',
        getTherapistSubscriptionDate: '/admin/subscription/therapist/valid/date',
        getStats: '/admin/therapist/details',
        updateTherapistMenus: '/admin/therapist/payment/toggle'
    },

    payment: {
        getAllPayments: "/admin/transactions/all",
        getAllClient: "/admin/clients/all",
        getAllSchedule: "/admin/schedules/all",
        createInvoice: "/admin//invoice/create",
        getAllInvoice: "/admin/invoices/all",
        invoiceStats: "/admin/invoices/stats",
        getPayouts: "/admin/payouts/all",
        getPayoutById: "/admin/payout",
        updatePayout: "/admin/payout/update",
        createPayout: "/admin/payout/create",
        getPendingPayout: "/admin/payout/pendings",
        makeTransfer: "/admin/payout/rzp/transfer",
        paymentVerify: "/admin/rzp/payment/verify"
    },

    deductions: {
        getPendingDeduction: "/admin/deduction/all",
        getAlldeductions: "/admin/deduction/all",
        createDeduction: "/admin/deduction/create",
        deleteDeduction: "/admin/deduction/delete",
        createInvoice: "/admin//invoice/create",
        getAllInvoice: "/admin/invoices/all",
        getDeductionStats: "/admin/deduction/stats",
        deleteInvoice: "/admin/invoice/delete"
    },

    clientTransaction: {
        getClientTransaction: "/admin/client/transactions/all",
        getTherapistPendingTransactions: '/admin/therapist/transactions/pending',
        deletePendingLinks: "/admin/transactions/pending/delete"
    },
    subscription: {
        createSubscription: '/admin/subscription/create',
        getSubscriptions: '/admin/subscription/get',
        toggleStatus: '/admin/subscription/toggle',
        deleteSubscription: '/admin/subscription' 
    },
    getExportData: '/admin/export',
    subscriptions: {
        getAllActiveSubscriptions: "/admin/subscription/paid/therapist/",
        getAllSubTransactions: "/admin/subscription/transactions/therapist/",
        refreshTransaction: "/admin/subscription/transactions/refresh",
        addSubscription: "/admin/subscription/therapist/create",
        getAllActiveSubscription: "/admin/subscription/get/active",
        generateTransactionReport: "/admin/transactions/export/csv/",
        getActiveSubscriptionCounts: "/admin/subscription/active/counts",
    },
    blog: {
        getAllBlogs: "/admin/blogs",
        getBlogByHandle: "/admin/blogs",
        createBlog: "/admin/blogs",
        updateBlog: "/admin/blogs",
        deleteBlog: "/admin/blogs",
        uploadFile: "/admin/upload-file"
    },

    notification: {
        getTherapistNotifications: "/notification/admin/get/therapist",
        // getAllNotifications:"/notification/admin/getAll",
        deleteNotification: "/notification/admin/delete",
        sendNotification: "/notification/admin/create",
        getAllSubTransactions: "/admin/subscription/transactions/therapist/",

    }


};

export default url;
