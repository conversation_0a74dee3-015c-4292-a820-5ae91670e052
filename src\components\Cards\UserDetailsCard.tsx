
import { useState, useEffect, useRef, ChangeEvent } from "react";
import { Button, ButtonGroup, Card, Col, Dropdown, Form, Row, Tabs } from "react-bootstrap";
import { FaDownload, FaLongArrowAltLeft } from "react-icons/fa";
import { FiEdit } from "react-icons/fi";
import { TherapistService } from "../../services/therapist.service";
import toast from "react-hot-toast";
import { useParams } from "react-router-dom";
import ReactDatePicker from "react-datepicker";
import DetailsForm from "./TherapistDetailsForm";
import moment from "moment";
import { SubscriptionService } from "../../services/subsription.service";
import { AdminServices } from "../../services/admin.service";
import TherapistBankDetails from "./TherapistBank";
import TherapistStats from "./TherapistStats";
import PendingLinksTable from "../Table/PendingLinks.tables";
import { makeParams } from "../../api/makeRequest";

// interface IUserDetailsCard {
//     data: any,
//     setData: any,
//     onSave: any
// }


const UserCard = () => {

    const [therapist, setTherapist] = useState<any>()
    const params: any = useParams();
    const [activeFilter, setActiveFilter] = useState<string>('day');
    const [activeIndex, setActiveIndex] = useState<number>(0);
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
        moment().subtract(15, "days").toDate(),
        new Date()
    ]);
    const [subscriptionDetails, setSubscriptionDetails] = useState<any>({});

    const [editableTherapist, setEditableTherapist] = useState({
        bankDetails: {},
        isDeleted: false,
        isVerified: false,
        settings: {},
        verificationDetails: {}
    })
    const [reportLink, setReportLink] = useState<string>(undefined);

    const onBankDetailChange = (value: any, name: string) => {
        setTherapist((prevData) => ({
            ...prevData,
            bankDetails: { ...prevData?.bankDetails, [name]: value }
        }))
    };


    const onSettingsChange = (value: any, name: string) => {
        setTherapist((prevData) => ({
            ...prevData,
            settings: { ...prevData?.settings, [name]: value },
        }))
        // therapist?.settings?.emailNotification
    };

    const onVerifiedChange = (value: any, name: string) => {
        setTherapist((prevData) => ({
            ...prevData,
            [name]: value
        }));
    };

    const downloadBlob = (blob: Blob, fileName: string) => {
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };


    function formatDate(date: any) {
        return moment(date).format('YYYY-MM-DD');
    }
    const getTherapistDetails = async () => {
        await TherapistService.getTherapistById(params.id, activeFilter, formatDate(dateRange[0]), formatDate(dateRange[1]))
            .then((res) => {
                if (res.status === 200) {
                    const therapist = res.data[0];
                    setEditableTherapist({
                        bankDetails: therapist.bankDetails,
                        isDeleted: therapist.isDeleted,
                        isVerified: therapist.isVerified,
                        settings: therapist.settings,
                        verificationDetails: therapist.verificationDetails
                    })
                    setTherapist(therapist);
                    // setVerificationDetails(therapist.verificationDetails);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }

    const updateTherapistDetails = async () => {
        const updatedData = { ...editableTherapist };
        return await TherapistService.updateTherapistDetails(params.id, updatedData).then((res) => {
            if (res.status === 200) {
                getTherapistDetails();
                toast.success("Details Updated successfully")
                return true;
            }
            else {
                toast.error("Error in updating details")
                return false;
            }
        }).catch(err => {
            toast.error(err.response.data)
            return false;
        })
    }


    const fileDownload = async (documentUrl: string) => {
        await AdminServices.downloadFile(documentUrl).then((res) => {
            if (res.status === 200) {
                const fileName = reportLink.split('/').pop() || 'download.xlsx';
                downloadBlob(new Blob([res.data]), fileName);
                toast.success("File Downloaded")
            }
        }).catch(e => {
            toast.error("Error in downloading file")
        })
    }

    const handleFileChange = async (e: any) => {
        const file = e.target.files[0];
        const formData = new FormData();
        formData.append('upload', file);
        formData.append('index', activeIndex.toString());
        formData.append('therapistId', params.id);

        await TherapistService.updateTherapistDocs(formData)
            .then((res) => {
                if (res.status === 200) {
                    toast.success("Profile Photo Updated");
                    // window.location.reload();
                    getTherapistDetails();
                }
            }).catch(err => {
                console.log(err)
                toast.error("Error in uploading profile photo");
            })
    }

    async function getSubscriptionDetails() {
        await SubscriptionService.getTherapistSubscriptionDetails(params.id)
            .then((res) => {
                if (res.status === 200) {
                    // console.log(res.data);
                    setSubscriptionDetails(res.data);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }

    useEffect(() => {
        if (params.id && dateRange[0] && dateRange[1]) {
            getTherapistDetails();
        }
    }, [params.id, activeFilter, dateRange]);

    useEffect(() => {
        getSubscriptionDetails();
    }, []);


    const onDetailsUpdate = (details: any) => {
        // console.log(details, "details");
        setEditableTherapist({ ...details })
    }


    const generateTransactionReport = async () => {
        await AdminServices.generateTransactionReport(params.id).then((res) => {
            if (res.status === 200) {
                setReportLink(res.data.link);
                fileDownload(res.data.link);
                toast.success("Report Generated")
            }
        }).catch(e => {
            toast.error("Error in generating report")
        })
    }

    async function handlePaymentMenusChange(e: ChangeEvent<HTMLInputElement>) {
        const { name, checked } = e.target;
        const query = makeParams([
            {index:'therapistId', value: params.id},
            {index: name, value: checked}
        ]);
        await TherapistService.updateTherapistMenus(query)
            .then((res) => {
                if (res.status === 200) {
                    toast.success("Menu Updated");
                    getTherapistDetails();
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }

    return (

        <>

            <Card className="shadow-sm mt-3">
                <FaLongArrowAltLeft className="ms-4 mt-2" style={{ fontSize: "20px", cursor: "pointer" }}
                    onClick={() => window.history.back()}
                />
                <Card.Body>
                    <Row>
                        <Col md={6}>
                            <Card.Text>
                                <b>Name:</b> <em className="em">{therapist?.name} </em>
                            </Card.Text>
                            <Card.Text>
                                <b>Email:</b> <em className="em">{therapist?.email} </em>
                            </Card.Text>
                            <Card.Text>
                                <b>Id:</b> <em className="em">{therapist?.identifier} </em>
                            </Card.Text>
                            <Card.Text>
                                <b>Linkedin_URL:</b> <em className="em">{therapist?.linkedin_url} </em>
                            </Card.Text>
                            <Card.Text>
                                <b>Address:</b> <em className="em">{therapist?.address?.streetAddress}, {therapist?.address?.district}, {therapist?.address?.state}, {therapist?.address?.pincode}</em>
                            </Card.Text>
                            <Card.Text>
                                <b>Registered(GST)/Unregistered : </b> <em className="em">{therapist?.gstNumber ? (
                                <em className="em">Registered / <b>GST: </b> {therapist?.gstNumber} </em>
                                ) : <em className="em">Unregistered</em>} </em>
                            </Card.Text>
                        </Col>
                        <Col md={6}>
                            <b>Subscription Details</b>
                            {
                                subscriptionDetails.valid ?
                                    <>
                                        <Card.Text className="mb-0">
                                            <em className="em">{subscriptionDetails?.message} </em>
                                        </Card.Text>
                                        <Card.Text>
                                            <b>Remaining Days: </b><em className="em">{subscriptionDetails?.validDays} </em>
                                        </Card.Text>
                                    </>
                                    :
                                    <>
                                        <Card.Text className="mb-0">
                                            <em className="em">No Valid Subscription </em>
                                        </Card.Text>
                                    </>
                            }
                        </Col>
                    </Row>
                    <Row className="mt-3">
                        <Col md={4}>
                            <Button size="sm" onClick={generateTransactionReport}>
                                Download Transaction Report
                            </Button>
                        </Col>
                    </Row>
                    <hr />
                    <Row>
                        <h6>Payment Menus</h6>
                        <div className="mt-2 d-flex align-items-center">
                            <Form.Switch className="ms-3" label="Payment Gateway" name="togglePaymentGateway" checked={therapist?.menus?.paymentGateway} onChange={handlePaymentMenusChange} />
                            <Form.Switch className="ms-3" label="Payment Tracker" name="togglePaymentTracker" checked={therapist?.menus?.paymentTracker} onChange={handlePaymentMenusChange} />
                        </div>
                    </Row>

                    {/* <Row className="mt-3 p-2 bg-light p-3 rounded">
                        <Col md={3}>
                            <div className="bg-white p-3 rounded">
                                <Form.Group>
                                    <Form.Label>
                                        <b>Date Range:</b>
                                    </Form.Label>
                                    <ReactDatePicker
                                        className="w-auto form-control mt-2 d-flex gap-2"
                                        selectsRange={true}
                                        startDate={startDate}
                                        endDate={endDate}
                                        onChange={(update) => {
                                            setDateRange(update);
                                        }}
                                        withPortal
                                        dateFormat={'dd/MM/yyyy'}
                                        isClearable
                                    />
                                </Form.Group>
                            </div>
                        </Col>
                        <Col md={9}>
                            <Row>
                                <Col md={3}>
                                    <b>Sessions:</b> <em className="em">{therapist?.totalSessionsWithFilter} </em>
                                </Col>
                                <Col md={3}>
                                    <b>Cancelled Sessions:</b> <em className="em">{therapist?.cancelledSessions} </em>

                                </Col>
                                <Col md={3}>
                                    <b>Scheduled Sessions:</b> <em className="em">{therapist?.upcomingSessions} </em>
                                </Col>
                            </Row>
                            <Row className="mt-3">
                                <Col md={3}>
                                    <b>Total Sessions:</b> <em className="em">{therapist?.totalSessions} </em>

                                </Col>
                                <Col md={3}>
                                    <b>Payments Collected:</b> <em className="em">{therapist?.totalCollection} </em>
                                </Col>
                            </Row>
                        </Col>
                    </Row> */}
                </Card.Body>
            </Card>
            <TherapistBankDetails
                data={editableTherapist}
                reloadData={getTherapistDetails}
                onUpdate={onDetailsUpdate}
                onSave={updateTherapistDetails}
            />
            <Card className="shadow-sm mt-3">
                <Card.Body>
                    <DetailsForm
                        data={editableTherapist}
                        reloadData={getTherapistDetails}
                        onUpdate={onDetailsUpdate}
                        onSave={updateTherapistDetails}
                    />

                    <Row>
                        <div className="mt-3">Documents: </div>
                        <Row>
                            {
                                therapist && therapist.verificationDetails && therapist?.verificationDetails?.docs && therapist?.verificationDetails?.docs.map((doc: any, index: number) => (
                                    <Col md={2} key={index} className="mb-2">
                                        <Card className="text-center">
                                            <Card.Body onClick={() => fileDownload(doc)}>
                                                <div>
                                                    Doc {index + 1}
                                                </div>
                                                <div className="mt-2">
                                                    Download <FaDownload />
                                                </div>
                                            </Card.Body>
                                        </Card>
                                    </Col>
                                ))
                            }
                        </Row>
                    </Row>

                </Card.Body>


            </Card>
            <TherapistStats />

            <Card className="px-2 my-3">
                <Row>
                    <div className="mt-3 fw-bold">
                        Pending Links:
                    </div>

                    <div className="mb-3">
                        <PendingLinksTable therapistId={params?.id} />
                    </div>
                </Row>
            </Card>

        </>
    );
};

export default UserCard;
