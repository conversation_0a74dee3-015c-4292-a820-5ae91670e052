import makeRequest, { makeParams } from "../api/makeRequest"
import { RequestMethods } from "../api/requestMethode"
import url from "../api/urls"

export class PaymentService {

    static async getAllInvoices(pageNumber: any, pageSize: any, fromDate: any, toDate: any, therapist: any) {

        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "fromDate",
                value: fromDate
            },
            {
                index: "toDate",
                value: toDate
            },
            {
                index: "therapistId",
                value: therapist
            },
        ])
        return await makeRequest(url.payment.getAllInvoice + params, RequestMethods.GET)
    }

    static async getPendingInvoices(pageNumber: any, pageSize: any, isNotPaid: any, therapistId: any) {
        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "isNotPaid",
                value: isNotPaid
            }, {
                index: "therapistId",
                value: therapistId
            }
        ])
        return await makeRequest(url.payment.getAllInvoice + params, RequestMethods.GET)
    }

    static async getAllPayments(pageNumber: any, pageSize: any) {

        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            }
        ])
        return await makeRequest(url.payment.getAllPayments + params, RequestMethods.GET)
    }

    static async getAllClients(pageNumber: any, pageSize: any, therapistId: any) {
        const params = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "therapistId",
                value: therapistId
            },
        ])

        return await makeRequest(url.payment.getAllClient + params, RequestMethods.GET)
    }

    static async getAllSchedule(pageNumber: any, pageSize: any, therapistId: any, clientId: any) {
        const params = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "therapistId",
                value: therapistId
            },
            {
                index: "clientId",
                value: clientId
            },
        ])

        return await makeRequest(url.payment.getAllSchedule + params, RequestMethods.GET)
    }

    static async createInvoice(payload: any) {
        return await makeRequest(url.payment.createInvoice, RequestMethods.POST, payload)
    }

    static async getAllDeductions(pageNumber: any, pageSize: any) {
        const params = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            }
        ])

        return await makeRequest(url.deductions.getAlldeductions + params, RequestMethods.GET)
    }

    static async createDeduction(payload: any, id: any) {
        return await makeRequest(url.deductions.createDeduction + "/" + id, RequestMethods.POST, payload)
    }

    static async deleteDeduction(id: any) {
        return await makeRequest(url.deductions.deleteDeduction + "/" + id, RequestMethods.DELETE)
    }
    static async deleteInvoice(id: any) {
        return await makeRequest(url.deductions.deleteInvoice + "/" + id, RequestMethods.DELETE)
    }

    static async getPendingDeduction(pageNumber: any, pageSize: any, isNotPaid: any, therapistId: any) {
        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "isNotPaid",
                value: isNotPaid
            }, {
                index: "therapistId",
                value: therapistId
            }
        ])
        return await makeRequest(url.deductions.getPendingDeduction + params, RequestMethods.GET)
    }
    static async getDeductionStats() {
        return await makeRequest(url.deductions.getDeductionStats, RequestMethods.GET)
    }

    static async getInvoiceStats() {
        return await makeRequest(url.payment.invoiceStats, RequestMethods.GET)
    }

    static async getPayouts(pageNumber: number, pageSize: number, therapistId?: any, status?: any) {
        const params = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "therapistId",
                value: therapistId
            },
            {
                index: "status",
                value: status
            }
        ])
        return await makeRequest(url.payment.getPayouts + params, RequestMethods.GET)
    }

    static async getPayoutbyId(id: any, populated?: boolean) {
        const params = makeParams([{
            index: "populated",
            value: populated
        }])
        return await makeRequest(url.payment.getPayoutById + "/" + id + params, RequestMethods.GET)
    }

    static async updatePayout(payoutId: any, payload: any) {
        return await makeRequest(url.payment.updatePayout + "/" + payoutId, RequestMethods.PUT, payload)
    }

    static async createPayout(therapistId: string, payload: any) {
        return await makeRequest(url.payment.createPayout + '/' + therapistId, RequestMethods.POST, payload)
    }

    static async getPendingPayout() {
        return await makeRequest(url.payment.getPendingPayout, RequestMethods.GET);
    }

    static async makeTransfer(payoutId: any) {
        return await makeRequest(url.payment.makeTransfer + "/" + payoutId, RequestMethods.PUT)
    }
}