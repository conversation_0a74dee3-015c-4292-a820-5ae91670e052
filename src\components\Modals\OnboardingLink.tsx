import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Tooltip } from "react-bootstrap";

interface RefundPaymentProps {
    show: boolean;
    setShow: (show: boolean) => void;
}
export default function OnboardingLink({ show, setShow }: RefundPaymentProps) {
    const [formData, setFormData] = useState({
        name: '',
        email: ''
    });
    const [errors, setErrors] = useState<string>();

    const handleChange = (e: any) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    }
    const renderTooltip = (props: any) => (
        <Tooltip id="button-tooltip" {...props}>
            Send Link
        </Tooltip>
    );
    return (
        <>
            <Modal show={show} onHide={
                () => { setShow(false); }
            }>
                <Modal.Header className="border-0" closeButton>
                    <Modal.Title >Genrate Onboarding Link</Modal.Title>
                </Modal.Header>
                <Modal.Body className="pt-0">
                    <div className="form-group mb-3">
                        <label>Name <span className="text-danger">*</span></label>
                        <input type="text" className="form-control" name='name' value={formData.name} onChange={handleChange} required />
                    </div>
                    <div className="form-group mb-3">
                        <label>Email Address <span className="text-danger">*</span></label>
                        <input type="email" className="form-control" name='email' value={formData.email} onChange={handleChange} required >
                        </input>
                    </div>

                    <OverlayTrigger
                        placement="top"
                        delay={{ show: 250, hide: 400 }}
                        overlay={renderTooltip}
                    >
                        <div className="form-group mb-2">
                            <Button variant="primary" className="col-md-12 justify-content-center" onClick={() => { }} >
                                <span className="pe-2">Send the link</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M17.2063 10.3313L11.5813 15.9563C11.4924 16.0391 11.3749 16.0842 11.2534 16.082C11.132 16.0799 11.0161 16.0307 10.9302 15.9448C10.8444 15.8589 10.7952 15.743 10.793 15.6216C10.7909 15.5002 10.8359 15.3826 10.9187 15.2938L15.743 10.4688H3.125C3.00068 10.4688 2.88145 10.4194 2.79354 10.3315C2.70564 10.2436 2.65625 10.1243 2.65625 10C2.65625 9.87571 2.70564 9.75648 2.79354 9.66857C2.88145 9.58066 3.00068 9.53128 3.125 9.53128H15.743L10.9187 4.70628C10.8359 4.61742 10.7909 4.49989 10.793 4.37845C10.7952 4.25701 10.8444 4.14115 10.9302 4.05526C11.0161 3.96938 11.132 3.92018 11.2534 3.91804C11.3749 3.9159 11.4924 3.96098 11.5813 4.04378L17.2063 9.66878C17.294 9.75667 17.3433 9.87581 17.3433 10C17.3433 10.1242 17.294 10.2434 17.2063 10.3313Z" fill="#2C2C2C" />
                                </svg>
                            </Button>
                        </div>
                    </OverlayTrigger>
                    <div className="form-group mb-3">
                        {errors && <label className='text-danger'>{errors}</label>}
                    </div>

                </Modal.Body>
            </Modal>
        </>
    )
}