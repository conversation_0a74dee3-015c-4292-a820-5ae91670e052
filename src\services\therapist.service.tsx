import makeRequest, { makeParams } from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";
import url from "../api/urls";

export class TherapistService {
    static async getAllTherapist(pageNumber: any, pageSize: any, isVerified?: any) {

        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "isVerified",
                value: isVerified
            }
        ])
        return await makeRequest(url.therapist.getAllTherapist + params, RequestMethods.GET)
    }

    static async getAllTherapistCount(isVerified?: boolean) {
        let params = makeParams([
            {
                index: "isVerified",
                value: isVerified
            }])
        return await makeRequest(url.therapist.getAllTherapistCount + params, RequestMethods.GET)
    }

    static async getTherapistById(id: string, filter?: string, fromDate?: any, toDate?: any) {

        const params = makeParams([
            {
                index: "therapist",
                value: id
            },
            {
                index: "filter",
                value: filter
            },
            {
                index: "fromDate",
                value: fromDate
            },
            {
                index: "toDate",
                value: toDate
            }
        ])

        return await makeRequest(url.therapist.getAllTherapist + params, RequestMethods.GET)
    }

    static async updateBankDetails(id: string, data: any) {
        return await makeRequest(`${url.therapist.updateBankDetails}/${id}`, RequestMethods.PUT, data)
    }

    static async updateTherapistDetails(id: any, payload: any) {
        delete payload.profilePhoto;
        delete payload.documents;
        return await makeRequest(url.therapist.updateTherapistDetails + "/" + id, RequestMethods.PUT, payload)
    }

    static async getAllSessionsById(pageNumber: any, pageSize: any) {

        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            }
        ])
        return await makeRequest(url.therapist.getSessions + params, RequestMethods.GET)
    }

    static async getTherapistStats() {
        return await makeRequest(url.therapist.getTherapistStats, RequestMethods.GET)
    }

    static async paymentVerify(id: any) {
        return await makeRequest(url.payment.paymentVerify + "/" + id, RequestMethods.POST)
    }

    static async updateTherapistDocs(formData: any) {
        return await makeRequest(url.therapist.updateTherapistDocs, RequestMethods.PUT, formData);
    }

    static async createNotification(payload: any) {
        return await makeRequest(url.notification.sendNotification, RequestMethods.POST, payload)
    }

    static async getAllNotifications(therpistId: string) {
        return await makeRequest(url.notification.getTherapistNotifications + '/' + therpistId, RequestMethods.GET);
    }

    static async deleteNotificationById(notificationId: string) {
        if (notificationId === null || notificationId === undefined) {
            console.error("Received null/undefined notificationId in deleteNotificationById");
            return;
        }
        return makeRequest(`${url.notification.deleteNotification}/${notificationId}`, RequestMethods.DELETE);
    }

    static async getTherapistDetailStats(therapistId: string, fromDate?: any, toDate?: any) {
        const params = makeParams([
            {
                index: "fromDate",
                value: fromDate
            },
            {
                index: "toDate",
                value: toDate
            }
        ]);

        return makeRequest(url.therapist.getStats + '/' + therapistId + params, RequestMethods.GET);
    }

    static async getTherapistPendingTransActions(therapistId: string) {
        return makeRequest(url.clientTransaction.getTherapistPendingTransactions + '/' + therapistId, RequestMethods.GET);
    }

    static async deletePendingLinks(linkId: any) {
        return makeRequest(url.clientTransaction.deletePendingLinks + "/" + linkId, RequestMethods.DELETE)
    }

    static async updateTherapistMenus(query: any) {
        return makeRequest(url.therapist.updateTherapistMenus + query, RequestMethods.PUT)
    }
}