
import { useEffect, useState } from 'react'
import { Button, Col, Form, Modal, ModalBody, ModalTitle, Row } from 'react-bootstrap'
import Select from 'react-select'
import { TherapistService } from '../../services/therapist.service'
import ReactDatePicker from 'react-datepicker'
import moment from 'moment'
import { PaymentService } from '../../services/payment.service'
import toast from 'react-hot-toast'

interface IDeductionModal {
    show: boolean,
    setShow: any,
    getAllDeductions: any
}



const DeductionModal = ({ show, setShow, getAllDeductions }: IDeductionModal) => {


    enum DeductionTypeEnum {
        FINE = "FINE",
        PLATFORMFEE = "PLATFORM FEE",
        GATEWAYCHARGE = "GATEWAY CHARGE",
        TRANSFERCHARGE = "TRANSFER CHARGE"
    }

    const [therapistIds, setTherapistIds] = useState<any>([])
    const [pageNumber, setPageNumber] = useState<number>(1)
    const [pageSize, setPageSize] = useState<number>(999)
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [amount, setAmount] = useState<number>(0);
    const [type, setType] = useState<string>("FINE");
    const [deductionDate, setDeductionDate] = useState<Date>(new Date());





    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(pageNumber, pageSize).then((res) => {
            if (res.status === 200) {
                setTherapistIds(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }



    const createDeduction = async () => {
        const payload = {

            amount: amount,
            deductionType: type,
            deductionDate: moment(deductionDate).toISOString()
        }


        await PaymentService.createDeduction(
            payload,
            selectTherapist


        ).then((res) => {
            if (res.status === 200) {
                getAllDeductions();
                toast.success("Deduction created successfully")
            }
        }
        )


    }

    useEffect(() => {
        getAllTherapist();
    }, [])
    return (
        <Modal
            show={show}
            onHide={() => setShow(false)}
        >
            <Modal.Header closeButton>
                <Modal.Title>Create Deduction</Modal.Title>

            </Modal.Header>

            <ModalBody>

                <Row>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Therapist</Form.Label>
                        <Select options={therapistIds} onChange={(e: any) => setSelectTherapist(e.value)} />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Amount</Form.Label>
                        <Form.Control type="number" placeholder="Enter Amount" onChange={(e: any) => setAmount(e.target.value)} />

                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Type</Form.Label>
                        <Form.Select onChange={(e: any) => setType(e.target.value)}>
                            <option value={DeductionTypeEnum.FINE}>Fine</option>
                            <option value={DeductionTypeEnum.PLATFORMFEE}>Platform Fee</option>
                            <option value={DeductionTypeEnum.GATEWAYCHARGE}>Gateway Charge</option>
                            <option value={DeductionTypeEnum.TRANSFERCHARGE}>Transfer Charge</option>
                        </Form.Select>
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label className="text-muted">Deduction Date</Form.Label>
                        <div>

                        <ReactDatePicker
                            selected={deductionDate}
                            onChange={(date: any) => setDeductionDate(date)}
                            dateFormat="dd/MM/yyyy"
                            className="form-control"
                        />
                        </div>
                    </Form.Group>

                    <Button variant="primary" onClick={
                        () => {
                            setShow(false)
                            createDeduction();
                        }



                    }>
                        Create Deduction
                    </Button>


                </Row>
            </ModalBody>

        </Modal>
    )
}
export default DeductionModal