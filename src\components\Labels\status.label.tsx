import { Badge } from "react-bootstrap"

interface IStatusLabelProps {
    status: "PENDING" | "PAID" | "CANCELLED" | "COMPLETE" | "PAID OFFLINE"
}

export default function StatusLabel(props: IStatusLabelProps) {
    switch (props.status) {
        case "PENDING":
            return <Badge bg="warning">Pending</Badge>
        case "PAID":
            return <Badge bg="success">Paid</Badge>
        case "CANCELLED":
            return <Badge bg="danger">Cancelled</Badge>
        case "COMPLETE":
            return <Badge bg="secondary">Completed</Badge>
        case "PAID OFFLINE":
        return <Badge bg="info">PAID OFFLINE</Badge>
    }

}