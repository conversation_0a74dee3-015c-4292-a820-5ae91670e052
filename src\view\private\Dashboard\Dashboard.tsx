import { useEffect, useState } from "react";
import { Card, Col, Container, Form, Row } from "react-bootstrap";
import DataCard from "../../../components/Cards/DataCard";
import { AdminServices } from "../../../services/admin.service";
import ReactDatePicker from "react-datepicker";
import moment from "moment";

export default function Dashboard() {
    const [statsData, setStatsData] = useState<any>();
    const [detailStatsData, setDetailStatsData] = useState<any>({});
    const [dateRange, setDateRange] = useState([moment().subtract(30, 'days').toDate(), new Date()]);

    const [activeSubscriptionCounts, setActiveSubscriptionCounts] = useState<any>({});
    const [subscriptionNames, setSubscriptionNames] = useState<any>({});

    // We'll get subscription IDs dynamically from the API response

    const getActiveSubscriptionCounts = async () => {
        try {
            const res = await AdminServices.getActiveSubscriptionCounts();
            console.log("Active Subscription Counts Response:", res);
            if (res.status === 200) {
                // Extract subscription data from the response
                const subscriptionsData = res.data.subscriptions || [];

                // Create a map of subscription IDs to their counts and names
                const subscriptionCountsMap = {};
                const subscriptionNamesMap = {};

                subscriptionsData.forEach((subscription: any) => {
                    subscriptionCountsMap[subscription._id] = subscription.count;
                    subscriptionNamesMap[subscription._id] = subscription.name;
                });

                setActiveSubscriptionCounts(subscriptionCountsMap);
                setSubscriptionNames(subscriptionNamesMap);

                console.log("Processed subscription counts:", subscriptionCountsMap);
                console.log("Processed subscription names:", subscriptionNamesMap);
            }
        } catch (err) {
            console.error("Failed to fetch subscription counts", err);
        }
    };

    // We no longer need the getSubscriptionNames function as we get names directly from the API

    const getAllStats = async () => {
        await AdminServices.getStats().then((res) => {
            if (res.status === 200) {
                setStatsData(res.data);
            }
        });
    };

    const getDetailStats = async () => {
        await AdminServices.getDashboardDetailsStats(moment(dateRange[0]).format("YYYY-MM-DD"), moment(dateRange[1]).format("YYYY-MM-DD"))
            .then((res) => {
                if (res.status === 200) {
                    setDetailStatsData(res.data);
                }
            })
            .catch((err) => {
                console.log(err);
            });
    };

    useEffect(() => {
        getActiveSubscriptionCounts();
    }, []);

    useEffect(() => {
        getAllStats();
    }, []);

    useEffect(() => {
        getDetailStats();
    }, [dateRange]);

    // We no longer need to filter subscription IDs as we're getting them directly from the API
    // Just use the activeSubscriptionCounts directly

    return (
        <Container>
            <Row>
                <Col>
                    <DataCard title={"Therapist"} subTitle={""} value={statsData?.therapist || 0} color="#F5C8BD" />
                </Col>
                <Col>
                    <DataCard title={"Sessions booked"} subTitle={""} value={statsData?.sessions || 0} color="#A7E8E0" />
                </Col>
                <Col>
                    <DataCard title={"Received Payments"} subTitle={""} value={statsData?.transactions || 0} color="#E2BEE2" />
                </Col>
                <Col>
                    <DataCard title={"Pending Payments"} subTitle={""} value={statsData?.pending || 0} color="#9CBCE4" />
                </Col>
            </Row>

            <Row className="mt-4">
            {activeSubscriptionCounts &&
                Object.entries(activeSubscriptionCounts).map(([subscriptionId, count]: any, index: number) => (
                    <Col md={4} key={subscriptionId}>
                        <DataCard
                            title={`Active: ${subscriptionNames[subscriptionId] || subscriptionId}`}
                            subTitle=""
                            value={count}
                            color={["#FFD580", "#C6E2FF", "#D5A6BD", "#D0F0C0"][index % 4]}
                        />
                    </Col>
                ))}
            </Row>

            <Card className="mt-3 border-0">
                <Card.Body>
                    <Row>
                        <Col md={3}>
                            <Form.Group className="p-2 mb-3 card border-0">
                                <Form.Label className="text-muted">Date Range</Form.Label>
                                <ReactDatePicker
                                    selected={dateRange[0]}
                                    onChange={(e) => setDateRange(e)}
                                    startDate={dateRange[0]}
                                    endDate={dateRange[1]}
                                    selectsRange
                                    className="form-control"
                                    dateFormat={"dd/MM/yyyy"}
                                />
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row>
                        <Col>
                            <DataCard title={"Total Payment Collection"} subTitle={""} value={detailStatsData?.totalCollection || 0} color="#A7E8E0" />
                        </Col>
                        <Col>
                            <DataCard title={"All Sessions"} subTitle={""} value={detailStatsData?.allSessions || 0} color="#E2BEE2" />
                        </Col>
                        <Col>
                            <DataCard title={"Google Synced Sessions"} subTitle={""} value={detailStatsData?.googleSyncedSessions || 0} color="#F5C8BD" />
                        </Col>
                    </Row>

                    <Row className="mt-4">
                        {detailStatsData?.subscriptionCounts &&
                            Object.entries(detailStatsData.subscriptionCounts).map(([plan, count]: any, index: number) => (
                                <Col key={index}>
                                    <DataCard
                                        title={`Subscriptions: ${plan}`}
                                        subTitle={""}
                                        value={count}
                                        color={["#FFD580", "#C6E2FF", "#D5A6BD"][index % 3]} // Rotate colors
                                    />
                                </Col>
                            ))}
                    </Row>


                </Card.Body>
            </Card>
        </Container>
    );
}
