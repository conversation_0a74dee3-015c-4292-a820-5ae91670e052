interface IStaticMutedText {
    label: string
    value: string
}

export function StaticMutedText(props: IStaticMutedText) {
    return (
        <div className='d-flex justify-content-start align-items-center'>
            <div className='fw-bold fs-medium text-muted'>
                {props.label}
            </div>
            <div className='ms-1 fs-medium'>
                {props.value}
            </div>
        </div>
    )
}