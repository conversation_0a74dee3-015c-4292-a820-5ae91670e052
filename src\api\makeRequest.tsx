import axios, { Method, ResponseType } from "axios";
import axios<PERSON>and<PERSON> from "../helpers/axiosHandler";

export interface IParams {
  value: any;
  index: string;
}

export default async function makeRequest(
  url: string,
  method: Method,
  inputPayload?: any,
  responseType?: ResponseType
) {
  let requestConfig = {
    baseURL: `${import.meta.env.VITE_REACT_APP_API_URL}${import.meta.env.VITE_REACT_APP_API_VER}`,
    url: url,
    method: method,
    headers: {
      Authorization: sessionStorage.getItem("authKey") || "",
    },
    data: {},
    responseType: responseType || "json",
  };

  if (method !== "get" && inputPayload) {
    requestConfig.data = inputPayload;
  }

  try {
    let response = await axios.request(requestConfig);
    return response;
  } catch (error: any) {
    axiosHandler(error);
    throw error;
  }
}

export function makeParams(params: IParams[]) {
  let paramString = "?";
  for (const param in params) {
    if (params[param].value !== undefined && params[param].value !== null) {
      if (Number(param) != 0) paramString = paramString + "&";
      paramString =
        paramString + params[param].index + "=" + params[param].value;
    }
  }
  return paramString;
}
