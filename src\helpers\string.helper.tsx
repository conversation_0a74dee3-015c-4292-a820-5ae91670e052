export default class StringHelper {
  static capitalizeFirstLetter(sentence: string): string {
    if (sentence.length === 0) {
      return sentence; // Return an empty string if the input is empty
    }
    sentence = sentence.toLowerCase();
    const firstLetter = sentence[0].toUpperCase();
    const restOfSentence = sentence.slice(1);

    return firstLetter + restOfSentence;
  }

  static formatPhoneNumber(phoneNumber) {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const regex = /^(\d{2})(\d{5})(\d{5})$/;
    const formattedNumber = cleaned.replace(regex, '+$1-$2-$3');

    return formattedNumber;
  }

  static truncateString(str: string, words?: number) {
    if (str.length <= 70) {
      return str;
    } else {
      return str.substring(0, words || 100) + '...';
    }
  }

}