import { useEffect, useState } from "react"
import { PaymentService } from "../../../services/payment.service";
import { <PERSON><PERSON>, Col, Container, Pagination, Row } from "react-bootstrap";
import DeductionTable from "../../../components/Table/DeductionTable";
import DeductionModal from "../../../components/Modals/DeductionModal";
import DataCard from "../../../components/Cards/DataCard";


const Deduction = () => {

  const [deductionsData, setDeductionsData] = useState([]);
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(10)

  const [showDeductionModal, setShowdeductionModal] = useState<boolean>(false)

  const [stats, setStats] = useState<any>({});

  let items: any = [];

  for (let number = 1; number <= pageNumber; number++) {
    items.push(
      <Pagination.Item key={number} active={number === pageNumber}>
        {number}
      </Pagination.Item>
    );
  }
  const getAllDedeuctions = async () => {
    await PaymentService.getAllDeductions(pageNumber, pageSize)
      .then((res) => {
        if (res.status === 200) {
          setDeductionsData(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
  }


  const getDeductionStats = async () => {
    await PaymentService.getDeductionStats()
      .then((res) => {
        if (res.status === 200) {
          setStats(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      })
  }



  useEffect(() => {
    getAllDedeuctions();
    getDeductionStats()
  }, [pageNumber, pageSize]);


  return (
    <Container>

      <Row >
        <Col >
          <DataCard title={"Total"} text={"Count"} subTitle={""} value={stats?.totalDeductions || 0} color="#F5C8BD" />
        </Col>
        <Col >
          <DataCard title={"Last 30 days Count"} text={"Count"} subTitle={""} value={stats?.last30DaysDeductions || 0} color="#9CBCE4" />
        </Col>
        <Col >
          <DataCard title={"Amount in Last 30 days"} text={"Count"} subTitle={""} value={stats?.deductionAmount30Days || 0} color="#E2BEE2" />
        </Col>
      </Row>

      <div className="d-flex justify-content-between align-items-center mt-3">
        <span className=" fw-bold">
          Deductions:
        </span>
        <Button size="sm" variant="secondary" onClick={() => setShowdeductionModal(true)}>Create</Button>
      </div>

      <DeductionTable tableData={deductionsData}
        getAllDeductions={getAllDedeuctions}
      />
      <div className="px-0 px-md-3 mt-2">
        <div className="d-flex flex-column flex-md-row justify-content-md-between">
          <div>
            <Pagination>
              <Pagination.Prev onClick={() => setPageNumber(pageNumber - 1)} />
              {items}
              <Pagination.Next onClick={() => setPageNumber(pageNumber + 1)} />
            </Pagination>
          </div>
          <div>
            <div className='row'>
              <div className="col pe-md-0">
                <span className="small flex-grow-1">Record Per Page </span>
              </div>
              <div className="col-auto">
                <select className="form-select bg-light form-select-sm" aria-label=".form-select-sm example" onChange={(e: any) => { setPageSize(e.target.value) }}>
                 
                  <option value="1">10</option>
                  <option value="2">15</option>
                  <option value="3">20</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DeductionModal show={showDeductionModal} setShow={setShowdeductionModal}
        getAllDeductions={getAllDedeuctions}
      />
    </Container>
  )
}

export default Deduction