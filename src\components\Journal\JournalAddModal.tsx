import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Table, Al<PERSON>, Spinner } from "react-bootstrap";
import { JournalService } from "../../services/journal.service";
import { BlogService } from "../../services/blog.service";

interface Blog {
  _id: string;
  blogName: string;
  description: string;
  handle: string;
  featureImage: string;
  writerName: string;
  isVisible: boolean;
  publishDate: string;
}

interface JournalEntry {
  blogID: {
    _id: string;
    blogName: string;
  };
  position: number;
}

interface JournalAddModalProps {
  show: boolean;
  onHide: () => void;
  onAdd: (selectedBlogs: Blog[]) => void;
  existingJournals: JournalEntry[];
}

const JournalAddModal: React.FC<JournalAddModalProps> = ({
  show,
  onHide,
  onAdd,
  existingJournals
}) => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (show) {
      fetchBlogs();
      setSelectedBlogs([]);
      setSearchTerm("");
    }
  }, [show]);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try public blogs first, then fallback to all blogs
      let response;
      try {
        response = await JournalService.getAllPublicBlogs();
      } catch (publicError) {
        console.log("Public blogs endpoint failed, trying fallback...");
        response = await BlogService.getAllBlogsNoPagination();
      }

      if (response.data && response.data.success) {
        const blogData = response.data.data;
        if (Array.isArray(blogData)) {
          setBlogs(blogData);
        } else if (blogData && Array.isArray(blogData.blogs)) {
          setBlogs(blogData.blogs);
        } else {
          setBlogs([]);
        }
      } else {
        throw new Error(response.data?.message || "Failed to fetch blogs");
      }
    } catch (error: any) {
      console.error("Error fetching blogs:", error);
      setError(error.response?.data?.message || error.message || "Failed to fetch blogs");
      setBlogs([]);
    } finally {
      setLoading(false);
    }
  };

  const handleBlogSelection = (blogId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedBlogs(prev => [...prev, blogId]);
    } else {
      setSelectedBlogs(prev => prev.filter(id => id !== blogId));
    }
  };

  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      const availableBlogIds = getAvailableBlogs().map(blog => blog._id);
      setSelectedBlogs(availableBlogIds);
    } else {
      setSelectedBlogs([]);
    }
  };

  const getAvailableBlogs = () => {
    const existingBlogIds = existingJournals.map(journal => journal.blogID._id);
    return blogs.filter(blog => !existingBlogIds.includes(blog._id));
  };

  const getFilteredBlogs = () => {
    const availableBlogs = getAvailableBlogs();
    if (!searchTerm) return availableBlogs;
    
    return availableBlogs.filter(blog =>
      blog.blogName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.writerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const handleAdd = () => {
    const selectedBlogObjects = blogs.filter(blog => selectedBlogs.includes(blog._id));
    onAdd(selectedBlogObjects);
  };

  const filteredBlogs = getFilteredBlogs();
  const availableBlogs = getAvailableBlogs();

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>Add Journals</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {loading ? (
          <div className="text-center py-4">
            <Spinner animation="border" variant="primary" />
            <p className="mt-2">Loading blogs...</p>
          </div>
        ) : error ? (
          <Alert variant="danger">
            <Alert.Heading>Error Loading Blogs</Alert.Heading>
            <p>{error}</p>
            <Button variant="outline-danger" onClick={fetchBlogs}>
              Retry
            </Button>
          </Alert>
        ) : (
          <>
            {/* Search and Select All */}
            <div className="mb-3">
              <Form.Control
                type="text"
                placeholder="Search blogs by name, writer, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-2"
              />
              
              {availableBlogs.length > 0 && (
                <Form.Check
                  type="checkbox"
                  label={`Select All Available (${availableBlogs.length} blogs)`}
                  checked={selectedBlogs.length === availableBlogs.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              )}
            </div>

            {/* Blog Selection Table */}
            {filteredBlogs.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-muted">
                  {availableBlogs.length === 0 
                    ? "All blogs are already added to the journal section."
                    : "No blogs found matching your search."
                  }
                </p>
              </div>
            ) : (
              <div style={{ maxHeight: "400px", overflowY: "auto" }}>
                <Table responsive striped hover>
                  <thead>
                    <tr>
                      <th width="50">Select</th>
                      <th>Blog Name</th>
                      <th>Writer</th>
                      <th>Status</th>
                      <th>Image</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredBlogs.map((blog) => (
                      <tr key={blog._id}>
                        <td>
                          <Form.Check
                            type="checkbox"
                            checked={selectedBlogs.includes(blog._id)}
                            onChange={(e) => handleBlogSelection(blog._id, e.target.checked)}
                          />
                        </td>
                        <td>
                          <div>
                            <strong>{blog.blogName}</strong>
                            <br />
                            <small className="text-muted">
                              {blog.description?.substring(0, 80)}...
                            </small>
                          </div>
                        </td>
                        <td>{blog.writerName || "Unknown"}</td>
                        <td>
                          <span className={`badge ${blog.isVisible ? 'bg-success' : 'bg-secondary'}`}>
                            {blog.isVisible ? 'Published' : 'Draft'}
                          </span>
                        </td>
                        <td>
                          {blog.featureImage ? (
                            <img 
                              src={blog.featureImage} 
                              alt={blog.blogName}
                              style={{ width: "40px", height: "30px", objectFit: "cover" }}
                              className="rounded"
                            />
                          ) : (
                            <div 
                              className="bg-light d-flex align-items-center justify-content-center rounded"
                              style={{ width: "40px", height: "30px" }}
                            >
                              <small className="text-muted">No Image</small>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            )}

            {/* Selection Summary */}
            {selectedBlogs.length > 0 && (
              <Alert variant="info" className="mt-3">
                <strong>{selectedBlogs.length}</strong> blog(s) selected for addition to journal section.
              </Alert>
            )}
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button 
          variant="primary" 
          onClick={handleAdd}
          disabled={selectedBlogs.length === 0 || loading}
        >
          Add Selected ({selectedBlogs.length})
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default JournalAddModal;
