import React, { useEffect, useState } from "react";
import { JournalService } from "../../services/journal.service";
import { BlogService } from "../../services/blog.service";

interface Blog {
  _id: string;
  blogName: string;
  description: string;
  handle: string;
  featureImage: string;
  writerName: string;
  isVisible: boolean;
  publishDate: string;
}

interface JournalEntry {
  blogID: {
    _id: string;
    blogName: string;
  };
  position: number;
}

interface JournalAddModalProps {
  show: boolean;
  onHide: () => void;
  onAdd: (selectedBlogs: Blog[]) => void;
  existingJournals: JournalEntry[];
}

const JournalAddModal: React.FC<JournalAddModalProps> = ({
  show,
  onHide,
  onAdd,
  existingJournals
}) => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (show) {
      fetchBlogs();
      setSelectedBlogs([]);
      setSearchTerm("");
    }
  }, [show]);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try public blogs first, then fallback to all blogs
      let response;
      try {
        response = await JournalService.getAllPublicBlogs();
      } catch (publicError) {
        console.log("Public blogs endpoint failed, trying fallback...");
        response = await BlogService.getAllBlogsNoPagination();
      }

      if (response.data && response.data.success) {
        const blogData = response.data.data;
        if (Array.isArray(blogData)) {
          setBlogs(blogData);
        } else if (blogData && Array.isArray(blogData.blogs)) {
          setBlogs(blogData.blogs);
        } else {
          setBlogs([]);
        }
      } else {
        throw new Error(response.data?.message || "Failed to fetch blogs");
      }
    } catch (error: any) {
      console.error("Error fetching blogs:", error);
      setError(error.response?.data?.message || error.message || "Failed to fetch blogs");
      setBlogs([]);
    } finally {
      setLoading(false);
    }
  };

  const handleBlogSelection = (blogId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedBlogs(prev => [...prev, blogId]);
    } else {
      setSelectedBlogs(prev => prev.filter(id => id !== blogId));
    }
  };

  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      const availableBlogIds = getAvailableBlogs().map(blog => blog._id);
      setSelectedBlogs(availableBlogIds);
    } else {
      setSelectedBlogs([]);
    }
  };

  const getAvailableBlogs = () => {
    const existingBlogIds = existingJournals.map(journal => journal.blogID._id);
    return blogs.filter(blog => !existingBlogIds.includes(blog._id));
  };

  const getFilteredBlogs = () => {
    const availableBlogs = getAvailableBlogs();
    if (!searchTerm) return availableBlogs;
    
    return availableBlogs.filter(blog =>
      blog.blogName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.writerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const handleAdd = () => {
    const selectedBlogObjects = blogs.filter(blog => selectedBlogs.includes(blog._id));
    onAdd(selectedBlogObjects);
  };

  const filteredBlogs = getFilteredBlogs();
  const availableBlogs = getAvailableBlogs();

  if (!show) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onHide}
        ></div>

        {/* Modal panel */}
        <div className="relative transform overflow-hidden rounded-xl bg-white shadow-2xl transition-all w-full max-w-4xl max-h-[90vh] flex flex-col">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">The Picklebay Journal</h3>
          </div>

          {/* Body */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading blogs...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-red-800 font-medium">Error Loading Blogs</h4>
                  <p className="text-red-600 mt-1">{error}</p>
                  <button
                    onClick={fetchBlogs}
                    className="mt-3 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : (
              <>
                {/* Blog Selection List */}
                {filteredBlogs.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      {availableBlogs.length === 0
                        ? "All blogs are already added to the journal section."
                        : "No blogs found matching your search."
                      }
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredBlogs.map((blog) => (
                      <div
                        key={blog._id}
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          selectedBlogs.includes(blog._id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handleBlogSelection(blog._id, !selectedBlogs.includes(blog._id))}
                      >
                        <div className="flex items-start space-x-4">
                          <input
                            type="checkbox"
                            checked={selectedBlogs.includes(blog._id)}
                            onChange={(e) => handleBlogSelection(blog._id, e.target.checked)}
                            className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {blog.blogName}
                            </h4>
                            <div className="mt-1 text-sm text-gray-500">
                              <div dangerouslySetInnerHTML={{
                                __html: blog.description?.substring(0, 120) + "..." || "No description available"
                              }} />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {blog.featureImage ? (
                              <img
                                src={blog.featureImage}
                                alt={blog.blogName}
                                className="w-16 h-12 object-cover rounded"
                              />
                            ) : (
                              <div className="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
                                <span className="text-xs text-gray-500">No Image</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t border-gray-200">
            <button
              onClick={onHide}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
            >
              Discard
            </button>
            <button
              onClick={handleAdd}
              disabled={selectedBlogs.length === 0 || loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JournalAddModal;
