import { useEffect, useState } from 'react'
import { Row, Col, Form, Button, Spinner } from 'react-bootstrap'
import toast from 'react-hot-toast';
import { FiEdit } from 'react-icons/fi';
// import PhoneInput from 'react-phone-input-2';
import Select, { ActionMeta } from 'react-select';
import { TherapistService } from '../../services/therapist.service';
import { useParams } from 'react-router-dom';
import { expOptions, featureOptions, genderOptions, loadOptions, socialOptions } from '../../helpers/data';

interface IProps {
  data: any;
  reloadData: any;
  onUpdate: any
  onSave: any
}
export default function DetailsForm({ data, reloadData , onUpdate, onSave}: IProps) {

  const [edit, setEdit] = useState<boolean>(false);

  const handleDetailsChange = (e:any) => {
    const verificationDetails = data.verificationDetails;
    verificationDetails[e.target.name] = e.target.value;
    onUpdate({...data, verificationDetails: verificationDetails});
  }

  const handleSelectChangeArr = (e:any, name: string) => {
    const selectedValues = e.map((data) => data.value);
    const verificationDetails = data.verificationDetails;
    verificationDetails[name] = selectedValues;
    onUpdate({...data, verificationDetails: verificationDetails});
  }

  const handleSelectChange = (e:any, name: string) => {
    const verificationDetails = data.verificationDetails;
    verificationDetails[name] = e.value;
    onUpdate({...data, verificationDetails: verificationDetails});
  }

  const onSaveClick = async() => {
    const save = await onSave();
    if(save){
      setEdit(false);
      reloadData();
    }
  }

  return (
    <div className=''>
      <div className="my-3 border-bottom d-flex justify-content-between w-auto pb-1">
        <b className="fs-4 p-0">Verification Details:</b>
        {edit ?
          <Button className="px-5" variant="success" size="sm" onClick={onSaveClick}>Save</Button> :
          <FiEdit style={{ fontSize: 20 }} onClick={() => setEdit(true)} />
        }
      </div>
      <div className="d-flex justify-content-end">
      </div>
      <Row>
        <Col md={6}>
          <Form.Group>
            <Form.Label>Age preference <span className="text-danger">*</span></Form.Label>
            <Form.Control type="number" max={100} min={1} name="agePreference" onChange={(e: any) => { handleDetailsChange(e) }} value={data?.verificationDetails?.agePreference} disabled={!edit} />
          </Form.Group>

          <Form.Group>
            <Form.Label>Gender preference <span className="text-danger">*</span></Form.Label>
            <Select options={genderOptions} name='genderPreference' isMulti onChange={(e:any) => handleSelectChangeArr(e , 'genderPreference')} isClearable value={genderOptions.filter((option) => data.verificationDetails?.genderPreference?.includes(option.value))} isDisabled={!edit} />
          </Form.Group>

          <Form.Group>
            <Form.Label>Practicing title (Eg:- psychotherapist, counsellor, clinical psychologist etc.) <span className="text-danger">*</span></Form.Label>
            <Form.Control type="text" name="practicingTitle" onChange={handleDetailsChange} value={data.verificationDetails?.practicingTitle} disabled={!edit} />
          </Form.Group>

          <Form.Group>
            <Form.Label>Client load <span className="text-danger">*</span></Form.Label>
            <Select options={loadOptions} name='clientLoad' onChange={(e:any) => handleSelectChange(e, 'clientLoad')} isClearable value={loadOptions.filter((option) => option.value === data.verificationDetails?.clientLoad)} isDisabled={!edit} />
          </Form.Group>
        </Col>
        <Col>
          <Form.Group>
            <Form.Label>Years of experience <span className="text-danger">*</span></Form.Label>
            <Select options={expOptions} isClearable name='yearsOfExperience' onChange={(e:any) => handleSelectChange(e, 'yearsOfExperience')} value={expOptions.filter((option) => option.value === data.verificationDetails?.yearsOfExperience)} isDisabled={!edit} />
          </Form.Group>

          <Form.Group>
            <Form.Label>What feature(s) are you most in need of ? <span className="text-danger">*</span></Form.Label>
            <Select options={featureOptions} isMulti isClearable name='featuresNeed' onChange={(e:any) => handleSelectChangeArr(e, 'featuresNeed')} value={featureOptions.filter((option) => data.verificationDetails?.featuresNeed?.includes(option.value))} isDisabled={!edit} />
          </Form.Group>

          <Form.Group>
            <Form.Label>How did you hear about us? <span className="text-danger">*</span></Form.Label>
            <Select options={socialOptions} isMulti name='source' onChange={(e:any) => handleSelectChangeArr(e, 'source')} isClearable value={socialOptions.filter((option) => data.verificationDetails?.source?.includes(option.value))} isDisabled={!edit} />
          </Form.Group>
        </Col>
      </Row>
    </div >
  )
}