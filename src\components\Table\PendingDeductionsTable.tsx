import moment from "moment";
import { Table } from "react-bootstrap"

interface ITherapistTable {
    tableData: any,
    selectedIds: any,
    setSelectedIds: any
}

export default function PendingDeductionsTable({ tableData, selectedIds, setSelectedIds }: ITherapistTable) {

    const handleSelect = (e: any, id: any) => {
        if (e.target.checked) {
            setSelectedIds([...selectedIds, id])
        } else {
            const filteredIds = selectedIds.filter((data: any) => data !== id)
            setSelectedIds(filteredIds)
        }
        // console.log(selectedIds)
    }

    const handleSelectAll = (e: any) => {
        if (e.target.checked) {
            const ids = tableData.map((data: any) => data._id)
            setSelectedIds(ids)
        } else {
            setSelectedIds([])
        }
    };

    const isCheckboxChecked = (id: any) => {
        return selectedIds.includes(id);
    }

    const formattedDate = (date: any) => {
        return moment(date).format("DD-MM-YYYY HH:mm")
    }
    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            {/* <th>
                                <input type="checkbox" onChange={handleSelectAll} />
                            </th> */}
                            <th>Sr.No</th>
                            <th>Created at</th>
                            <th>Deduction Date</th>
                            <th>Amount</th>
                            <th>Deduction Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData && tableData.length > 0 ? tableData.map((data: any, index: number) => {
                            return (
                                <tr>
                                    {/* <td>
                                        <input type="checkbox" onChange={(e: any) => { handleSelect(e, data._id) }} checked={isCheckboxChecked(data._id)} />
                                    </td> */}
                                    <td>{index + 1}</td>
                                    <td>{formattedDate(data?.createdAt)}</td>
                                    <td>{formattedDate(data?.deductionDate)}</td>
                                    <td>{data?.amount}</td>
                                    <td>{data?.deductionType}</td>
                                </tr>
                            )
                        }) :
                            <tr>
                                <td colSpan={6} className="text-center">No data found</td>
                            </tr>
                        }
                    </tbody>
                </Table>
            </div>
        </div>
    )
}