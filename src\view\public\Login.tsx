import { <PERSON>, Button } from "react-bootstrap";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { BsArrowRight } from "react-icons/bs";
import ReCAPTCHA from "react-google-recaptcha";
import { useState } from "react";
import { AuthService } from "../../services/auth.service";
import Auth from "../../components/Auth/auth";

type FormData = {
  email: string;
  password: string;
};

const Login = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>();


  // const [captcha, setCaptcha] = useState<any>();
  const [errorMessage, setErrorMessage] = useState('');
  const [userData, setUserData] = useState({
    email: "",
    password: "",
  });

  const handleLogin = async () => {
    if (!userData.email || !userData.password) {
      return;
    }
    await AuthService.Login(userData)
      .then((res) => {
        sessionStorage.setItem("authKey", res.data.data.token);
        Auth.authenticate();
        window.location.href = "/dashboard"
        setErrorMessage('');
      })
      .catch((err) => {
        // console.log(err);
        setErrorMessage(err.response.data);
      });
  }

  return (
    <div className="container">
      <div className="d-flex justify-content-center align-items-center min-vh-100 ">
        <div
          style={{
            width: "400px",
            border: "2px solid #9FDEA5",
            padding: "20px",
            borderRadius: "10px",
          }}
        >
          <h3 className="text-center mb-4">Login to Therapist Admin</h3>
          <Form.Group controlId="email">
            <Form.Label className="text-secondary ms-1">
              User Name
            </Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter email"
              {...register("email", { required: "Email is required" })}
              className={`${errors.email ? "is-invalid" : ""} `}
              onChange={(e) => setUserData({ ...userData, email: e.target.value })}
            />
            {errors.email && (
              <Form.Text className="text-danger ms-1">
                {errors.email.message}
              </Form.Text>
            )}
          </Form.Group>

          <Form.Group controlId="password">
            <Form.Label className="text-secondary ms-1 mt-2">
              Password
            </Form.Label>
            <Form.Control
              type="password"
              placeholder="Password"
              {...register("password", { required: "Password is required" })}
              className={`${errors.password ? "is-invalid" : ""} `}
              onChange={(e) => setUserData({ ...userData, password: e.target.value })}
            />
            {errors.password && (
              <Form.Text className="text-danger ms-1">
                {errors.password.message}
              </Form.Text>
            )}
          </Form.Group>
          {/* <div className="mt-2">
            <ReCAPTCHA
              sitekey={`${import.meta.env.VITE_REACT_APP_API_VER}` || ""}
              onChange={(value: any) => setCaptcha(value)}
              onExpired={() => setCaptcha("")}
            />
          </div> */}

          <Button
            variant="primary"
            type="submit"
            style={{
              backgroundColor: "#9FDEA5",
              borderColor: "#9FDEA5",
              width: "100%",
              color: "black",
            }}
            className="w-full mt-3"
            // disabled={!captcha}
            onClick={handleLogin}
          >
            Log In <BsArrowRight />
          </Button>
          {errorMessage && (
            <Form.Text className="text-danger ms-1">
              {errorMessage}
            </Form.Text>
          )}
        </div>
      </div>
    </div>
  );
};

export default Login;