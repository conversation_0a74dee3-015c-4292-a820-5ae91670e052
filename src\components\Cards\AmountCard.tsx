import { Badge } from "react-bootstrap";

interface AmountCardProps {
    amount: number;
    therapist: string;
    client: string;
}

export default function AmountCard({ amount, therapist, client }: AmountCardProps) {
    return (
        <div className="shadow p-3 rounded d-flex justify-content-between" >
            <div>
                Amount Rs:{amount} to therapist {therapist} by client {client}
            </div>
            <Badge bg="success">Success</Badge>
        </div>
    )
}