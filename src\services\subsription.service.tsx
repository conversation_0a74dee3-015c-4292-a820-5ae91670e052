import makeRequest, { makeParams } from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";
import url from "../api/urls";

export class SubscriptionService {
    static async createSubscription(data: any) {
        return await makeRequest(url.subscription.createSubscription, RequestMethods.POST, data);
    }

    static async getSubscriptions(pageNumber: any, pageSize: any) {
        const params: any = makeParams([
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            }
        ])
        return await makeRequest(url.subscription.getSubscriptions + params, RequestMethods.GET);
    }

    static async toggleStatus(id: any) {
        return await makeRequest(url.subscription.toggleStatus + '/' + id, RequestMethods.PUT);
    }

    static async addSubscription(payload: any) {
        return await makeRequest(url.subscriptions.addSubscription, RequestMethods.POST, payload);
    }

    static async getAllActiveSubscription() {
        return await makeRequest(url.subscriptions.getAllActiveSubscription, RequestMethods.GET);
    }

    static async getTherapistSubscriptionDetails(id:any){
        return await makeRequest(url.therapist.getTherapistSubscriptionDate + '/' + id,RequestMethods.GET);
    }

    static async deleteSubscription(id: any) {
        return await makeRequest(`${url.subscription.deleteSubscription}/${id}`, RequestMethods.DELETE);
    }
    
}