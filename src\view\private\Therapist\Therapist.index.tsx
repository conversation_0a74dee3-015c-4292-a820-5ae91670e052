import { Col, Container, Pagination, Row, Tabs, Tab } from "react-bootstrap";
import DataCard from "../../../components/Cards/DataCard";
import { useEffect, useState } from "react";
import { TherapistService } from "../../../services/therapist.service";
import TherapistTable from "../../../components/Table/Therapist.table";
import TablePagination from "../../../components/Pagination/Table.paginaition";


export default function Therapist() {
    const [activeTab, setActiveTab] = useState<string>("All");
    const [therapist, setTherapist] = useState([]);
    const [pageNumber, setPageNumber] = useState<number>(1)
    const [pageSize, setPageSize] = useState<number>(10)
    const [therapistStats, setTherapistStats] = useState<any>();
    const [loading, setLoading] = useState<boolean>(false);
    const [count, setCount] = useState<number>(0);

    let items: any = [];

    for (let number = 1; number <= pageNumber; number++) {
        items.push(
            <Pagination.Item key={number} active={number === pageNumber}>
                {number}
            </Pagination.Item>
        );
    }


    const getAllTherapist = async () => {
        let isVerified;
        if (activeTab == "All") {
            isVerified = undefined
        }
        if (activeTab == "Verified") {
            isVerified = true
        } if (activeTab == "NotVerified") {
            isVerified = false
        }

        setLoading(true);
        await TherapistService.getAllTherapist(pageNumber, pageSize, isVerified)
            .then((res) => {
                if (res.status === 200) {
                    setTherapist(res.data);
                }
            })
            .catch((err) => {
                console.log(err);
            })
            .finally(() => {
                setLoading(false);
            })
    };

    const getTherapistStats = async () => {
        await TherapistService.getTherapistStats().then((res) => {
            if (res.status === 200) {
                setTherapistStats(res.data)
            }
        })
    }

    async function getTherapistCount() {
        let isVerified: boolean = false;
        if (activeTab == "All") {
            isVerified = undefined
        }
        if (activeTab == "Verified") {
            isVerified = true
        } if (activeTab == "NotVerified") {
            isVerified = false
        }
        await TherapistService.getAllTherapistCount(isVerified)
            .then((res) => {
                if (res.status === 200) {
                    // console.log(res.data)
                    setCount(res.data?.count || 0);
                }
            })
            .catch((err) => {
                console.log(err);
            })
    }


    useEffect(() => {
        getTherapistStats();
    }, [])

    useEffect(() => {
        getAllTherapist();
        getTherapistCount();
    }, [pageNumber, pageSize, activeTab]);

    return (
        <Container className="mt-3">
            <Row >
                <Col >
                    <DataCard title={"Therapist"} text={"Count"} subTitle={""} value={therapistStats?.therapist || 0} color="#F5C8BD" />
                </Col>
                <Col >
                    <DataCard title={"Approved"} text={"Count"} subTitle={""} value={therapistStats?.approved || 0} color="#9CBCE4" />
                </Col>
                <Col >
                    <DataCard title={"Signed up in last 30 Days"} text={"Count"} subTitle={""} value={therapistStats?.therapist_last_30_days || 0} color="#E2BEE2" />
                </Col>
            </Row>
            <Row className="mt-3">
                <Tabs activeKey={activeTab} onSelect={(tabKey) => setActiveTab(tabKey)}>
                    <Tab eventKey="All" title="All">

                    </Tab>
                    <Tab eventKey="Verified" title="Verified" >

                    </Tab>
                    <Tab eventKey="NotVerified" title="Not verified">

                    </Tab>
                </Tabs>
            </Row>
            <Row className="mt-3">
                <div className="mt-3 fw-bold">Therapist:</div>
                <div>
                    <TherapistTable tableData={therapist} loading={loading} />
                </div>
                <div className="px-0 px-md-3 mt-2">
                    <div className="bg-white py-2 px-3">
                        <TablePagination
                            total={count}
                            currentPage={pageNumber}
                            perPage={pageSize}
                            handlePageChange={(e: number) => {
                                setPageNumber(e)
                            }}
                            setPerPage={(e: number) => { setPageSize(e); setPageNumber(1) }}
                        />
                    </div>
                </div>
            </Row>
        </Container>
    )
}
