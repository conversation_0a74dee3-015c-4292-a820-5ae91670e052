import { Badge, Dropdown, Table } from "react-bootstrap"
import { FaEllipsisV, FaEye } from "react-icons/fa";
import CustomToggle from "../Menu/CustomMenu";
import { MdDelete } from "react-icons/md";

import { format } from "date-fns";

import { useNavigate } from "react-router-dom";
import { PaymentService } from "../../services/payment.service";
import toast from "react-hot-toast";


interface IDeductionTable {
    tableData: any
    getAllDeductions: any
}

export default function DeductionTable({ tableData , getAllDeductions}: IDeductionTable) {
    

    const naviagte = useNavigate();

    const deleteDeduction =  async  (id: any) => {
        await PaymentService.deleteDeduction(id).then((res) => {
            if (res.status === 200) {
                getAllDeductions();
                toast.success("Deduction deleted successfully")
            }
        }
        )
    }


    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Amount</th>
                            <th>Deduction Type</th>
                            <th>Deduction Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData && tableData?.length > 0 ? tableData?.map((data: any, index: number) => {
                            return (
                                <tr key={index+1}>
                                    <td>{index + 1}</td>
                                    <td>{data?.therapistId?.name || "--"}</td>
                                    <td>{data?.therapistId?.email || "--"}</td>
                                    <td>{data?.amount || "--"}</td>
                                    <td>{data?.deductionType || "--"}</td>
                                    <td>{data?.deductionDate ? format(new Date(data?.deductionDate), "dd/MM/yyyy") : "--"}</td>
                                    
                                    

                                    <td style={{cursor:"pointer"}}>
                                        <Dropdown>
                                            <Dropdown.Toggle
                                                as={CustomToggle}
                                                id="dropdown-custom-components"
                                            >
                                                <FaEllipsisV className="cursor-pointer" />
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu>
                                                <Dropdown.Item
                                                onClick={
                                                    () => {
                                                        deleteDeduction(data._id)

                                                    }
                                                }
                                                >
                                                    <MdDelete className="text-secondary" />{" "}{" "}
                                                    <span className="fw-bold text-secondary fs-12">
                                                        delete
                                                    </span>
                                                </Dropdown.Item>

                                            </Dropdown.Menu>
                                        </Dropdown>
                                    </td>
                                </tr>
                            )
                        }) : "No data found"}
                    </tbody>
                </Table>
            </div>
        </div>
    )
}