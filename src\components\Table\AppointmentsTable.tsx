import { Badge, Dropdown, Table } from "react-bootstrap"
import { FaEllipsisV, FaEye } from "react-icons/fa";
import CustomToggle from "../Menu/CustomMenu";

import { format } from "date-fns";
import { useState } from "react";
import { useNavigate } from "react-router-dom";


interface IAppointmentTable {
    tableData: any
}

export default function AppointmentTable({ tableData }: IAppointmentTable) {
    

    const naviagte = useNavigate();

    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Recurrence</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData && tableData?.appointments?.length > 0 ? tableData?.appointments.map((data: any, index: number) => {
                            return (
                                <tr>
                                    <td>{index + 1}</td>
                                    <td>{data?.therapistId?.name || "--"}</td>
                                    <td>{data?.therapistId?.email || "--"}</td>
                                    <td>{data?.fromDate ? format(new Date(data?.fromDate), "dd/MM/yyyy") : "--"}</td>
                                    <td>{data?.toDate ? format(new Date(data?.toDate), "dd/MM/yyyy") : "--"}</td>
                                    <td>
                                        <Badge>
                                            {data?.status}
                                        </Badge>

                                    </td>
                                    <td>{data?.recurrence || "--"}</td>

                                    <td style={{cursor:"pointer"}}>
                                        <Dropdown>
                                            <Dropdown.Toggle
                                                as={CustomToggle}
                                                id="dropdown-custom-components"
                                            >
                                                <FaEllipsisV className="cursor-pointer" />
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu>
                                                <Dropdown.Item
                                                onClick={
                                                    () => {
                                                        naviagte(`/appointments/${data?._id}`)
                                                    }
                                                }
                                                >
                                                    <FaEye className="text-secondary" />{" "}{" "}
                                                    <span className="fw-bold text-secondary fs-12">
                                                        View
                                                    </span>
                                                </Dropdown.Item>

                                            </Dropdown.Menu>
                                        </Dropdown>
                                    </td>
                                </tr>
                            )
                        }) : "No data found"}
                    </tbody>
                </Table>
            </div>
        </div>
    )
}