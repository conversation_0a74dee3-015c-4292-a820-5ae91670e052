import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Row, Col, Form, Table, Modal } from "react-bootstrap";
import toast from "react-hot-toast";
import { JournalService } from "../../../services/journal.service";
import { BlogService } from "../../../services/blog.service";
import JournalAddModal from "../../../components/Journal/JournalAddModal";
import ConfirmationModal from "../../../components/common/ConfirmationModal";

interface JournalEntry {
  blogID: {
    _id: string;
    blogName: string;
    description: string;
    handle: string;
    featureImage: string;
    writerName: string;
    isVisible: boolean;
  };
  position: number;
}

interface JournalSection {
  _id: string;
  section: string;
  isVisible: boolean;
  heading: string;
  subHeading: string;
  journals: JournalEntry[];
}

const Journal = () => {
  const [journalSection, setJournalSection] = useState<JournalSection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    fetchJournalSection();
  }, [refreshKey]);

  const fetchJournalSection = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await JournalService.getJournalSection();
      
      if (response.data && response.data.success) {
        setJournalSection(response.data.data);
      } else {
        // If no journal section exists, create a default one
        setJournalSection({
          _id: "",
          section: "journal",
          isVisible: true,
          heading: "Journal",
          subHeading: "Latest insights and articles",
          journals: []
        });
      }
    } catch (error: any) {
      console.error("Error fetching journal section:", error);
      setError(error.response?.data?.message || error.message || "Failed to fetch journal section");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleVisibility = async () => {
    if (!journalSection) return;

    try {
      const updatedSection = {
        ...journalSection,
        isVisible: !journalSection.isVisible
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        setJournalSection(updatedSection);
        toast.success(`Journal section ${updatedSection.isVisible ? 'enabled' : 'disabled'}`);
      } else {
        throw new Error(response.data?.message || "Failed to update visibility");
      }
    } catch (error: any) {
      console.error("Error updating visibility:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to update visibility");
    }
  };

  const handleUpdateHeadings = async (heading: string, subHeading: string) => {
    if (!journalSection) return;

    try {
      const updatedSection = {
        ...journalSection,
        heading,
        subHeading
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        setJournalSection(updatedSection);
        toast.success("Journal section updated successfully");
      } else {
        throw new Error(response.data?.message || "Failed to update section");
      }
    } catch (error: any) {
      console.error("Error updating section:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to update section");
    }
  };

  const handleAddJournals = async (selectedBlogs: any[]) => {
    if (!journalSection) return;

    try {
      const newJournals = selectedBlogs.map((blog, index) => ({
        blogID: blog._id,
        position: (journalSection.journals.length || 0) + index + 1
      }));

      const updatedSection = {
        ...journalSection,
        journals: [...(journalSection.journals || []), ...newJournals]
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        toast.success("Journals added successfully");
        setRefreshKey(prev => prev + 1);
        setShowAddModal(false);
      } else {
        throw new Error(response.data?.message || "Failed to add journals");
      }
    } catch (error: any) {
      console.error("Error adding journals:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to add journals");
    }
  };

  const handleRemoveJournal = async () => {
    if (!journalSection || !selectedJournalId) return;

    try {
      const updatedJournals = journalSection.journals.filter(
        journal => journal.blogID._id !== selectedJournalId
      );

      // Reorder positions
      const reorderedJournals = updatedJournals.map((journal, index) => ({
        ...journal,
        position: index + 1
      }));

      const updatedSection = {
        ...journalSection,
        journals: reorderedJournals
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        toast.success("Journal removed successfully");
        setRefreshKey(prev => prev + 1);
        setShowDeleteModal(false);
        setSelectedJournalId(null);
      } else {
        throw new Error(response.data?.message || "Failed to remove journal");
      }
    } catch (error: any) {
      console.error("Error removing journal:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to remove journal");
    }
  };

  const confirmDelete = (journalId: string) => {
    setSelectedJournalId(journalId);
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "400px" }}>
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading journal section...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-danger">
        <h5>Error loading journal section</h5>
        <p>{error}</p>
        <Button variant="primary" onClick={() => setRefreshKey(prev => prev + 1)}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="mb-0">Journal Management</h2>
        <Button 
          variant="primary" 
          onClick={() => setShowAddModal(true)}
          disabled={!journalSection}
        >
          Add Journals
        </Button>
      </div>

      {journalSection && (
        <>
          {/* Journal Section Info */}
          <Card className="mb-4">
            <Card.Header>
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Journal Section Settings</h5>
                <Form.Check 
                  type="switch"
                  id="visibility-switch"
                  label={journalSection.isVisible ? "Visible" : "Hidden"}
                  checked={journalSection.isVisible}
                  onChange={handleToggleVisibility}
                />
              </div>
            </Card.Header>
            <Card.Body>
              <JournalSectionForm 
                heading={journalSection.heading}
                subHeading={journalSection.subHeading}
                onUpdate={handleUpdateHeadings}
              />
            </Card.Body>
          </Card>

          {/* Journal Content Table */}
          <Card>
            <Card.Header>
              <h5 className="mb-0">Selected Journals ({journalSection.journals?.length || 0})</h5>
            </Card.Header>
            <Card.Body>
              {journalSection.journals && journalSection.journals.length > 0 ? (
                <JournalTable 
                  journals={journalSection.journals}
                  onRemove={confirmDelete}
                />
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted">No journals selected yet.</p>
                  <Button variant="outline-primary" onClick={() => setShowAddModal(true)}>
                    Add Your First Journal
                  </Button>
                </div>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {/* Add Journal Modal */}
      <JournalAddModal
        show={showAddModal}
        onHide={() => setShowAddModal(false)}
        onAdd={handleAddJournals}
        existingJournals={journalSection?.journals || []}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        show={showDeleteModal}
        onHide={() => {
          setShowDeleteModal(false);
          setSelectedJournalId(null);
        }}
        onConfirm={handleRemoveJournal}
        title="Remove Journal"
        message="Are you sure you want to remove this journal from the section?"
        confirmText="Remove"
        confirmVariant="danger"
      />
    </div>
  );
};

// Journal Section Form Component
const JournalSectionForm = ({ heading, subHeading, onUpdate }: {
  heading: string;
  subHeading: string;
  onUpdate: (heading: string, subHeading: string) => void;
}) => {
  const [formHeading, setFormHeading] = useState(heading);
  const [formSubHeading, setFormSubHeading] = useState(subHeading);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formHeading, formSubHeading);
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Section Heading</Form.Label>
            <Form.Control
              type="text"
              value={formHeading}
              onChange={(e) => setFormHeading(e.target.value)}
              placeholder="Enter section heading"
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Section Sub-heading</Form.Label>
            <Form.Control
              type="text"
              value={formSubHeading}
              onChange={(e) => setFormSubHeading(e.target.value)}
              placeholder="Enter section sub-heading"
            />
          </Form.Group>
        </Col>
      </Row>
      <Button type="submit" variant="primary">
        Update Section
      </Button>
    </Form>
  );
};

// Journal Table Component
const JournalTable = ({ journals, onRemove }: {
  journals: JournalEntry[];
  onRemove: (journalId: string) => void;
}) => {
  return (
    <Table responsive striped bordered hover>
      <thead>
        <tr>
          <th>Position</th>
          <th>Blog Name</th>
          <th>Image</th>
          <th>Writer</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {journals.map((journal, index) => (
          <tr key={journal.blogID._id}>
            <td>{journal.position}</td>
            <td>
              <div>
                <strong>{journal.blogID.blogName}</strong>
                <br />
                <small className="text-muted">
                  {journal.blogID.description?.substring(0, 100)}...
                </small>
              </div>
            </td>
            <td>
              {journal.blogID.featureImage ? (
                <img 
                  src={journal.blogID.featureImage} 
                  alt={journal.blogID.blogName}
                  style={{ width: "60px", height: "40px", objectFit: "cover" }}
                  className="rounded"
                />
              ) : (
                <div 
                  className="bg-light d-flex align-items-center justify-content-center rounded"
                  style={{ width: "60px", height: "40px" }}
                >
                  <small className="text-muted">No Image</small>
                </div>
              )}
            </td>
            <td>{journal.blogID.writerName || "Unknown"}</td>
            <td>
              <span className={`badge ${journal.blogID.isVisible ? 'bg-success' : 'bg-secondary'}`}>
                {journal.blogID.isVisible ? 'Published' : 'Draft'}
              </span>
            </td>
            <td>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => onRemove(journal.blogID._id)}
              >
                Remove
              </Button>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );
};

export default Journal;
