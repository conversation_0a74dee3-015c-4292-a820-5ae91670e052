import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { JournalService } from "../../../services/journal.service";
import { BlogService } from "../../../services/blog.service";
import JournalAddModal from "../../../components/Journal/JournalAddModal";
import ConfirmationModal from "../../../components/common/ConfirmationModal";
import { FaEdit, FaTrash, FaPencilAlt } from "react-icons/fa";

interface JournalEntry {
  blogID: {
    _id: string;
    blogName: string;
    description: string;
    handle: string;
    featureImage: string;
    writerName: string;
    isVisible: boolean;
  };
  position: number;
}

interface JournalSection {
  _id: string;
  section: string;
  isVisible: boolean;
  heading: string;
  subHeading: string;
  journals: JournalEntry[];
}

const Journal = () => {
  const [journalSection, setJournalSection] = useState<JournalSection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    fetchJournalSection();
  }, [refreshKey]);

  const fetchJournalSection = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await JournalService.getJournalSection();
      
      if (response.data && response.data.success) {
        setJournalSection(response.data.data);
      } else {
        // If no journal section exists, create a default one
        setJournalSection({
          _id: "",
          section: "journal",
          isVisible: true,
          heading: "Journal",
          subHeading: "Latest insights and articles",
          journals: []
        });
      }
    } catch (error: any) {
      console.error("Error fetching journal section:", error);
      setError(error.response?.data?.message || error.message || "Failed to fetch journal section");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleVisibility = async () => {
    if (!journalSection) return;

    try {
      const updatedSection = {
        ...journalSection,
        isVisible: !journalSection.isVisible
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        setJournalSection(updatedSection);
        toast.success(`Journal section ${updatedSection.isVisible ? 'enabled' : 'disabled'}`);
      } else {
        throw new Error(response.data?.message || "Failed to update visibility");
      }
    } catch (error: any) {
      console.error("Error updating visibility:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to update visibility");
    }
  };

  const handleUpdateHeadings = async (heading: string, subHeading: string) => {
    if (!journalSection) return;

    try {
      const updatedSection = {
        ...journalSection,
        heading,
        subHeading
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        setJournalSection(updatedSection);
        toast.success("Journal section updated successfully");
      } else {
        throw new Error(response.data?.message || "Failed to update section");
      }
    } catch (error: any) {
      console.error("Error updating section:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to update section");
    }
  };

  const handleAddJournals = async (selectedBlogs: any[]) => {
    if (!journalSection) return;

    try {
      const newJournals = selectedBlogs.map((blog, index) => ({
        blogID: blog._id,
        position: (journalSection.journals.length || 0) + index + 1
      }));

      const updatedSection = {
        ...journalSection,
        journals: [...(journalSection.journals || []), ...newJournals]
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        toast.success("Journals added successfully");
        setRefreshKey(prev => prev + 1);
        setShowAddModal(false);
      } else {
        throw new Error(response.data?.message || "Failed to add journals");
      }
    } catch (error: any) {
      console.error("Error adding journals:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to add journals");
    }
  };

  const handleRemoveJournal = async () => {
    if (!journalSection || !selectedJournalId) return;

    try {
      const updatedJournals = journalSection.journals.filter(
        journal => journal.blogID._id !== selectedJournalId
      );

      // Reorder positions
      const reorderedJournals = updatedJournals.map((journal, index) => ({
        ...journal,
        position: index + 1
      }));

      const updatedSection = {
        ...journalSection,
        journals: reorderedJournals
      };

      const response = await JournalService.updateJournalSection(updatedSection);
      
      if (response.data && response.data.success) {
        toast.success("Journal removed successfully");
        setRefreshKey(prev => prev + 1);
        setShowDeleteModal(false);
        setSelectedJournalId(null);
      } else {
        throw new Error(response.data?.message || "Failed to remove journal");
      }
    } catch (error: any) {
      console.error("Error removing journal:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to remove journal");
    }
  };

  const confirmDelete = (journalId: string) => {
    setSelectedJournalId(journalId);
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "400px" }}>
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading journal section...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-danger">
        <h5>Error loading journal section</h5>
        <p>{error}</p>
        <Button variant="primary" onClick={() => setRefreshKey(prev => prev + 1)}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-900">The Picklebay Journal</h1>
          <button
            onClick={() => setShowAddModal(true)}
            disabled={!journalSection}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Add Journal
          </button>
        </div>

        {/* Show Section Toggle */}
        {journalSection && (
          <div className="flex items-center space-x-3 text-sm">
            <span className="text-gray-600">Show Section:</span>
            <div className="flex items-center space-x-2">
              <span className={journalSection.isVisible ? "text-gray-400" : "text-gray-900"}>No</span>
              <button
                onClick={handleToggleVisibility}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  journalSection.isVisible ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    journalSection.isVisible ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={journalSection.isVisible ? "text-gray-900" : "text-gray-400"}>Yes</span>
            </div>
            <FaPencilAlt className="text-gray-400 ml-2 cursor-pointer hover:text-gray-600" />
          </div>
        )}
      </div>

      {/* Journal Table */}
      {journalSection && (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {journalSection.journals && journalSection.journals.length > 0 ? (
            <JournalTable
              journals={journalSection.journals}
              onRemove={confirmDelete}
            />
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No journals selected yet.</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Add Your First Journal
              </button>
            </div>
          )}
        </div>
      )}

      {/* Add Journal Modal */}
      <JournalAddModal
        show={showAddModal}
        onHide={() => setShowAddModal(false)}
        onAdd={handleAddJournals}
        existingJournals={journalSection?.journals || []}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        show={showDeleteModal}
        onHide={() => {
          setShowDeleteModal(false);
          setSelectedJournalId(null);
        }}
        onConfirm={handleRemoveJournal}
        title="Remove Journal"
        message="Are you sure you want to remove this journal from the section?"
        confirmText="Remove"
        type="danger"
      />
    </div>
  );
};



// Journal Table Component
const JournalTable = ({ journals, onRemove }: {
  journals: JournalEntry[];
  onRemove: (journalId: string) => void;
}) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Position
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Blog Name
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Image
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {journals.map((journal, index) => (
            <tr key={journal.blogID._id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {journal.position}
              </td>
              <td className="px-6 py-4">
                <div className="text-sm font-medium text-gray-900">
                  {journal.blogID.blogName}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {journal.blogID.featureImage ? (
                  <img
                    src={journal.blogID.featureImage}
                    alt={journal.blogID.blogName}
                    className="w-16 h-12 object-cover rounded"
                  />
                ) : (
                  <div className="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-xs text-gray-500">No Image</span>
                  </div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button className="text-blue-600 hover:text-blue-900">
                  <FaEdit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onRemove(journal.blogID._id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <FaTrash className="w-4 h-4" />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Journal;
