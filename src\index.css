.search-box {
    position: relative;
}

.search-box input {
    padding-left: 30px;
}

.search-box svg {
    position: absolute;
    left: 6px;
    top: 8px;
}


.applied-filter {
    border-radius: 0.90625rem;
    background: #ECECEC;
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.0075rem;
    position: relative;
    padding: .25rem .25rem .25rem .6rem;
    margin-left: .5rem;
}

.applied-filter:hover {
    background: #C7C7C7;
}

.delete-filter {
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 0.875rem;
    background: #C7C7C7;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-left: .25rem;
    cursor: pointer;
}

.applied-filter:hover .delete-filter {
    background-color: #FFD3C9;
}