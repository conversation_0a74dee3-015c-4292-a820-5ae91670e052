import React from 'react';
import { FaTrash, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';

interface ConfirmationModalProps {
  show: boolean;
  onHide: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  loading?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  show,
  onHide,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'info',
  loading = false,
}) => {
  if (!show) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'danger':
        return {
          icon: <FaTrash className="w-6 h-6" />,
          confirmButton: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
        };
      case 'warning':
        return {
          icon: <FaExclamationTriangle className="w-6 h-6" />,
          confirmButton: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white',
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
        };
      default:
        return {
          icon: <FaInfoCircle className="w-6 h-6" />,
          confirmButton: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white',
          iconBg: 'bg-blue-100',
          iconColor: 'text-blue-600',
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={!loading ? onHide : undefined}
        ></div>

        {/* Modal panel */}
        <div className="relative transform overflow-hidden rounded-xl bg-white shadow-2xl transition-all w-full max-w-md">
          <div className="bg-white px-6 pt-6 pb-4">
            <div className="flex flex-col items-center text-center">
              <div className={`flex h-16 w-16 items-center justify-center rounded-full ${styles.iconBg} mb-4`}>
                <span className={`${styles.iconColor}`}>
                  {styles.icon}
                </span>
              </div>
              <div className="w-full">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {title}
                </h3>
                <p className="text-sm text-gray-600 leading-relaxed mb-6">
                  {message}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-4 flex flex-col-reverse sm:flex-row sm:justify-center gap-3">
            <button
              type="button"
              className="inline-flex w-full sm:w-auto justify-center items-center h-10 px-6 rounded-lg bg-white text-gray-700 text-sm font-medium shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={onHide}
              disabled={loading}
            >
              {cancelText}
            </button>
            <button
              type="button"
              className={`inline-flex w-full sm:w-auto justify-center items-center h-10 px-6 rounded-lg text-sm font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${styles.confirmButton}`}
              onClick={onConfirm}
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                confirmText
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
