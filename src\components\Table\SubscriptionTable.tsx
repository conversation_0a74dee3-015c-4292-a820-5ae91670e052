import React from "react";
import { Bad<PERSON>, OverlayTrigger, Tooltip, Table, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { SubscriptionService } from "../../services/subsription.service";
import StringHelper from "../../helpers/string.helper";

export default function SubscriptionTable({ tableData, getData }: any) {
  const handleToggleStatus = async (id: any) => {
    await SubscriptionService.toggleStatus(id)
      .then((res) => {
        if (res.status === 200) {
          toast.success("Status changed");
          getData();
        }
      })
      .catch((error) => {
        console.log(error);
        toast.error("Error occurred");
      });
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this subscription?")) {
      try {
        const res = await SubscriptionService.deleteSubscription(id);
        if (res.status === 200) {
          toast.success("Subscription deleted");
          getData();
        }
      } catch (err) {
        console.error(err);
        toast.error("Failed to delete subscription");
      }
    }
  };

  if (!tableData || tableData.length === 0) {
    return (
      <div className="border rounded grid-border mt-2 p-3 text-center fw-bold text-muted">
        No Subscriptions to show
      </div>
    );
  }

  return (
    <div className="border rounded grid-border mt-2">
      <Table hover responsive className="text-center">
        <thead>
          <tr>
            <th>Sr.No</th>
            <th>Name</th>
            <th>Plan Price</th>
            <th>GST (18%)</th>
            <th>Razorpay Fee (2%)</th>
            <th>Price</th>
            <th>Currency</th>
            <th>Validity</th>
            <th>Description</th>
            <th>Status</th>
            <th>Change Status</th>
            {/* <th>Delete</th> */}
          </tr>
        </thead>
        <tbody>
          {tableData.map((data: any, index: number) => {
            const finalPrice = data.price || 0;

            const gst = finalPrice * 0.18;
            const razorpayFee = finalPrice * 0.02;
            const planPrice = finalPrice - gst - razorpayFee;

            return (
              <tr key={data._id}>
                <td>
                  <strong>{index + 1}</strong>
                </td>
                <td>{data?.name || "--"}</td>
                <td>₹{planPrice.toFixed(2)}</td>
                <td>₹{gst.toFixed(2)}</td>
                <td>₹{razorpayFee.toFixed(2)}</td>
                <td>₹{finalPrice.toFixed(2)}</td>
                <td>{data?.currency || "--"}</td>
                <td>{data?.validDays || "--"}</td>
                <td>
                  <OverlayTrigger
                    placement="top"
                    overlay={
                      <Tooltip id={`tooltip-${index}`}>
                        {data?.description}
                      </Tooltip>
                    }
                  >
                    <span style={{ cursor: "pointer" }}>
                      {StringHelper.truncateString(data?.description) || "--"}
                    </span>
                  </OverlayTrigger>
                </td>
                <td>
                  <Badge bg={data.status === "ACTIVE" ? "success" : "danger"}>
                    {data.status}
                  </Badge>
                </td>
                <td>
                  <Form.Switch
                    defaultChecked={data.status === "ACTIVE"}
                    onClick={() => handleToggleStatus(data._id)}
                  />
                </td>
                <td>
                  {/* <button
                    className="btn btn-sm btn-outline-danger"
                    onClick={() => handleDelete(data._id)}
                  >
                    Delete
                  </button> */}
                </td>
              </tr>
            );
          })}
        </tbody>
      </Table>
    </div>
  );
}
