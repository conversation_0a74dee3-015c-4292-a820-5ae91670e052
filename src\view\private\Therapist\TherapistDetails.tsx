import { useParams } from "react-router-dom"
import { TherapistService } from "../../../services/therapist.service";
import { useEffect, useState } from "react";
import UserCard from "../../../components/Cards/UserDetailsCard";
import toast from "react-hot-toast";
import TransactionCard from "../../../components/Cards/Transaction.card";
import { Button } from "react-bootstrap";

import Notifications from "../../../components/Cards/Notifications";
import AddSubscriptionModal from "../../../components/Modals/AddSubscriptiion.modal";


export default function TherapistDetails() {

    const params: any = useParams();
    const [showSubscriptionModal, setShowSubscriptionModal] = useState<any>(undefined)

    return (
        <>
            <div className="mt-2">
                <UserCard />
                <div className="mt-3 mb-3 bg-light p-4 rounded">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                        <h5 className="text-muted mb-2">
                            Subscriptions
                        </h5>
                        <Button size="sm" onClick={() => setShowSubscriptionModal(params.id)}>
                            Add Subscription
                        </Button>
                    </div>
                    <TransactionCard/>
                </div>
                <Notifications />
            </div>

            <AddSubscriptionModal
                show={showSubscriptionModal}
                handleClose={() => setShowSubscriptionModal(undefined)}
            />
        </>
    )
}   