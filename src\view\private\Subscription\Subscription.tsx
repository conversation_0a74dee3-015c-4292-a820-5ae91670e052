import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Col, Container, Form, InputGroup, Modal, Row } from 'react-bootstrap'
import { SubscriptionService } from '../../../services/subsription.service';
import toast from 'react-hot-toast';
import SubscriptionTable from '../../../components/Table/SubscriptionTable';
import TablePagination from '../../../components/Pagination/Table.paginaition';
import Select from "react-select"

const Subscription = () => {
    const [formData, setFormData] = useState<any>({
        isTrialIncluded: false,
        isAnnual: false,
        isMonthly: false,
        name: "",
        price: 0,
        currency: "INR",  
        subscriptionType: "BASIC",
        validDays: 0,
        description: ""
    });
    
    const [show, setShow] = useState<boolean>(false);
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(20);
    const [count, setCount] = useState<number>(0);
    const [subscriptions, setSubscriptions] = useState<any>();



    const SubscriptionType = [
        {
            label: "BASIC",
            value: "BASIC"
        },
        {
            label: "TRIAL PLAN",
            value: "TRIAL PLAN"
        },
    ]


    const SubscriptionCurrencyEnum = [
        {
            label: "INR",
            value: "INR"
        },
        {
            label: "USD",
            value: "USD"
        },
    ]





    const handleInputChange = (event: any) => {
        const { name, value } = event.target;
        setFormData((prevFormData: any) => ({
            ...prevFormData,
            [name]: value
        }));
    };

    const getSubscription = async () => {
        await SubscriptionService.getSubscriptions(pageNumber, pageSize).then((res) => {
            if (res.status == 200) {
                setCount(res.data.data.count);
                setSubscriptions(res.data.data.subscriptions);
            }
        })
            .catch((error) => {
                toast.error("Unable to get Subscription")
                console.log(error);
            })
    }


    const handleSubmitSubscription = async (e: any) => {
        await SubscriptionService.createSubscription(formData).then((res) => {
            if (res.status == 200) {
                toast.success("Subscription created successfully");
                setShow(false);
                getSubscription();
            }
        })
            .catch((error) => {
                console.log(error);
                toast.error("Unable to create Subscription")
            })
    }

    useEffect(() => {
        getSubscription();
    }, [])

    return (
        <>
            <div className='d-flex flex-row  align-items-center justify-content-between  w-auto '>
                <h5>
                    Subscriptions
                </h5>
                <Button type="button" onClick={() => { setShow(true) }}>Create Subscription</Button>
            </div>
            <div>
                <SubscriptionTable tableData={subscriptions} getData={getSubscription} />
            </div>
            <div className="mt-2">
                <TablePagination
                    total={count}
                    currentPage={pageNumber}
                    perPage={pageSize}
                    handlePageChange={(e: number) => {
                        setPageNumber(e)
                    }}
                    setPerPage={(e: number) => { setPageSize(e) }}
                />
            </div>

            <Modal show={show} onHide={() => { setShow(false) }}>
                <Modal.Header closeButton>
                    <Modal.Title>Create Subscription</Modal.Title>
                </Modal.Header>
                <Modal.Body> <Form.Group controlId="validationCustom01" className='mb-3 '>
                    <Form.Label>Name</Form.Label>
                    <Form.Control
                        type="text"
                        name='name'
                        placeholder="Name"
                        onChange={(e: any) => { handleInputChange(e) }}
                    />
                    <Form.Control.Feedback>Looks good!</Form.Control.Feedback>
                </Form.Group>
                    <Form.Group controlId="validationCustom02" className='mb-3 '>
                        <Form.Label>Price</Form.Label>
                        <Form.Control
                            type="number"
                            name='price'
                            placeholder="Price"
                            onChange={(e: any) => { handleInputChange(e) }}
                        />
                        <Form.Control.Feedback>Looks good!</Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group controlId="validationCustomUsername" className='mb-3 '>
                        <Form.Label>Currency</Form.Label>
                        <Select
                            className="w-100"
                            options={SubscriptionCurrencyEnum}
                            onChange={(e: any) => setFormData({ ...formData, currency: e.value })}
                        />
                    </Form.Group>
                    <Form.Group controlId="validationCustomUsername" className='mb-3 '>
                        <Form.Label>Subscription Type</Form.Label>
                        <Select
                            className="w-100"
                            options={SubscriptionType}
                            onChange={(e: any) => setFormData({ ...formData, subscriptionType: e.value })}
                        />
                    </Form.Group>
                    <Form.Group controlId="validationCustom03" className='mb-3 '>
                        <Form.Label>Validity(in Days)</Form.Label>
                        <Form.Control
                            type="number"
                            placeholder="Validity"
                            name='validDays'
                            onChange={(e: any) => { handleInputChange(e) }}
                        />
                    </Form.Group>
                    <Form.Group controlId="validationCustom03" className='mb-3 '>
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={3}
                            placeholder="Description"
                            name='description'
                            onChange={(e: any) => { handleInputChange(e) }}
                        />
                    </Form.Group>
                    <Form.Group controlId="isTrialIncluded" className="mb-3">
    <Form.Check
        type="checkbox"
        label="Trial Included"
        name="isTrialIncluded"
        checked={formData.isTrialIncluded || false}
        onChange={(e) => {
            const isChecked = e.target.checked;
            setFormData({
                ...formData,
                isTrialIncluded: isChecked,
                isMonthly: isChecked ? false : formData.isMonthly // Uncheck Monthly if Trial is included
            });
        }}
    />
</Form.Group>

<Form.Group controlId="isAnnual" className="mb-3">
    <Form.Check
        type="checkbox"
        label="Annual"
        name="isAnnual"
        checked={formData.isAnnual || false}
        onChange={(e) => {
            const isChecked = e.target.checked;
            setFormData({
                ...formData,
                isAnnual: isChecked,
                isMonthly: isChecked ? false : formData.isMonthly // Uncheck Monthly if Annual is selected
            });
        }}
    />
</Form.Group>

<Form.Group controlId="isMonthly" className="mb-3">
    <Form.Check
        type="checkbox"
        label="Monthly"
        name="isMonthly"
        checked={formData.isMonthly || false}
        disabled={formData.isAnnual || formData.isTrialIncluded} // Disable Monthly if Annual or Trial is selected
        onChange={(e) => {
            const isChecked = e.target.checked;
            setFormData({
                ...formData,
                isMonthly: isChecked,
                isAnnual: isChecked ? false : formData.isAnnual // Uncheck Annual if Monthly is selected
            });
        }}
    />
</Form.Group>



                    <Button type="submit" onClick={handleSubmitSubscription}>Create Subscription</Button></Modal.Body>
            </Modal>
        </>
    )
}

export default Subscription