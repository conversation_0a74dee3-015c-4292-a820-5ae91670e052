import makeRequest, { makeParams } from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";
import url from "../api/urls";

export class AdminServices {
    static async getStats() {
        return await makeRequest(url.dahsboard.getStats, RequestMethods.GET);
    }
    
    static async getTherapistById(therapistId: any) {
        return await makeRequest(`${url.therapist.getTherapistById}/${therapistId}`, RequestMethods.GET);
    }
    

    static async downloadFile(documentUrl:string){
        return await makeRequest(url.downloadFile, RequestMethods.POST, {documentUrl}, "blob");
    }

    static async getActiveSubscriptionCounts() {
        return await makeRequest(url.subscriptions.getActiveSubscriptionCounts, RequestMethods.GET);
    }

    static async getSubscriptionById(subscriptionId: string) {
        return await makeRequest(`${url.subscription.getSubscriptions}/${subscriptionId}`, RequestMethods.GET);
    }    
    
    static async getClientTransaction(therapistId: any, pageNumber: any, pageSize: any, status: any, fromDate: any, toDate: any) {
        const params = makeParams([
            {
                index: "therapistId",
                value: therapistId
            },
            {
                index: "pageNumber",
                value: pageNumber
            },
            {
                index: "pageSize",
                value: pageSize
            },
            {
                index: "status",
                value: status
            },
            {
                index: "fromDate",
                value: fromDate
            },
            {
                index: "toDate",
                value: toDate
            },
        ])
        return await makeRequest(url.clientTransaction.getClientTransaction + params, RequestMethods.GET);
    }

    static async getExportedData(payload: any) {
        return await makeRequest(url.getExportData, RequestMethods.POST, payload);
    }

    static async getAllActiveSubscriptions(therapistId: any) {
        return await makeRequest(url.subscriptions.getAllActiveSubscriptions + therapistId, RequestMethods.GET);
    }

    static async getAllSubTransactions(therapistId: any) {
        return await makeRequest(url.subscriptions.getAllSubTransactions + therapistId, RequestMethods.GET);
    }

    static async refreshTransaction(transactionId: any) {
        return await makeRequest(url.subscriptions.refreshTransaction+ "/" + transactionId, RequestMethods.PUT);
    }

    static async getDashboardDetailsStats(fromDate: any, toDate: any) {
        const params = makeParams([
            {
                index: "fromDate",
                value: fromDate
            },
            {
                index: "toDate",
                value: toDate
            }
        ]);
        return await makeRequest(url.dahsboard.getDetailStats+params, RequestMethods.GET)
    }

    static async generateTransactionReport(therapistId: any){
        return await makeRequest(url.subscriptions.generateTransactionReport + therapistId, RequestMethods.GET);
    }
}