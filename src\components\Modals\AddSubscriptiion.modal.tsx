import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Offcanvas } from "react-bootstrap";
import { TherapistService } from "../../services/therapist.service";
import { useEffect, useState } from "react";
import Select from "react-select"
import { SubscriptionService } from "../../services/subsription.service";
import toast from "react-hot-toast";

interface IAddSubscriptionModal {
    show: any,
    handleClose: any
}

export default function AddSubscriptionModal({ show, handleClose }: IAddSubscriptionModal) {


    // const [therapist, setTherapist] = useState<any>();
    const [daysLeft, setDaysLeft] = useState<boolean>(false);
    const [subscriptions, setSubscrtiptions] = useState<any>();
    const [data, setData] = useState<any>();
    const [subsData, setSubsData] = useState<any>();

    // const getAllTherapist = async () => {
    //     await TherapistService.getAllTherapist(1, 999).then((res) => {
    //         if (res.status === 200) {
    //             setTherapist(res.data.map((therapist: any) => {
    //                 return {
    //                     label: therapist.name + " " + `(${therapist.email})`,
    //                     value: therapist._id
    //                 }
    //             }))
    //         }
    //     })
    // }

    const getAllActiveSubscriptions = async () => {
        await SubscriptionService.getAllActiveSubscription().then((res) => {
            if (res.status === 200) {
                setSubsData(res.data.data.subscriptions)
                setSubscrtiptions(res.data.data.subscriptions.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.subscriptionType})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }

    const AddSubscription = async () => {
        await SubscriptionService.addSubscription({...data, therapistId: show}).then((res) => {
            if (res.status === 200) {
                toast.success("Subscription Added")
                handleClose();
                window.location.reload();
            }
        }).catch(err => {
            toast.error(err.response.data)
        })
    }

    useEffect(() => {
        // getAllTherapist();
        getAllActiveSubscriptions();
    }, [])
    return (
        <>
            <Offcanvas show={show ? true : false} onHide={handleClose} backdrop="static" placement="end" animation={true}>
                <Offcanvas.Header closeButton>
                    <Offcanvas.Title>Add Subscription</Offcanvas.Title>
                </Offcanvas.Header>
                <Offcanvas.Body>
                    <div style={{ minHeight: "84vh" }}>
                        {/* <Form.Group className="mb-3">
                            <Form.Label>Therapist</Form.Label>
                            <Select options={therapist} onChange={(e: any) => setData({ ...data, therapistId: e.value })} />
                        </Form.Group> */}
                        <Form.Group className="mb-3">
                            <Form.Label>Subscription</Form.Label>
                            <Select options={subscriptions} onChange={(e: any) => setData({ ...data, subscriptionId: e.value })} />
                            {data?.subscriptionId && (
                                <Alert className="mt-2" variant={"warning"}>
                                    {subsData && subsData.filter((subs: any) => subs._id === data?.subscriptionId) && (
                                        <span>{`${subsData.find((subs: any) => subs._id === data?.subscriptionId)?.currency} ${subsData.find((subs: any) => subs._id === data?.subscriptionId)?.price} - valid days ${subsData.find((subs: any) => subs._id === data?.subscriptionId)?.validDays}`}</span>
                                    )}
                                </Alert>
                            )}

                        </Form.Group>
                        <Form.Group className="mb-3 d-flex align-items-center">
                            <Form.Label>Is days left?</Form.Label>
                            <Form.Check className="ms-2 fs-4" checked={daysLeft} onChange={(e: any) => setDaysLeft(e.target.checked)} />
                        </Form.Group>
                        {daysLeft &&
                            <Form.Group>
                                <Form.Label>Enter Remaining days</Form.Label>
                                <Form.Control type="number" onChange={(e: any) => setData({ ...data, daysLeft: e.target.value })} />
                            </Form.Group>
                        }
                    </div>
                    <Button className="w-100" variant="secondary" onClick={AddSubscription}>Add</Button>
                </Offcanvas.Body>
            </Offcanvas>
        </>
    )
}