import { useState } from 'react';
import { Form } from 'react-bootstrap';
import Button from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';
import toast from 'react-hot-toast';
import { TherapistService } from '../../services/therapist.service';
import { useParams } from 'react-router-dom';

interface IModalProps {
  show: boolean;
  handleClose: () => void;
  reloadData:any;
}

function CreateNotificationModal({ show, handleClose, reloadData }: IModalProps) {
  const [message, setMessage] = useState<string>('');
  const params = useParams();

  console.log(params);
  async function sendNotification() {
    if (!message || message === '') {
      toast.error("Message cannot be empty");
      return;
    }

    if (!params?.id) {
      toast.error("Something went wrong");
      return;
    }

    let payload = {
      "therapistId": params?.id,
      "message": message
  }
    
    await TherapistService.createNotification(payload)
      .then((res) => {
        if(res.status === 200){
          toast.success("Notification Sent");
          reloadData();
          onModalClose();
        }
      })
      .catch((err) => {
        toast.error(err.response?.data);
        console.log(err);
      })
  }

  function onModalClose(){
    setMessage('');
    handleClose();
  }
  return (
    <>
      <Modal show={show} onHide={onModalClose}>
        <Modal.Header closeButton className='border-0'>
          <Modal.Title>Send New Notification</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group>
            <Form.Label>Message</Form.Label>
            <Form.Control as="textarea" rows={3} onChange={(e) => setMessage(e.target.value)} />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer className='border-0'>
          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>
          <Button variant="primary" onClick={sendNotification}>
            Send
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default CreateNotificationModal;