import Button from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';

interface IModalProps {
    show: boolean;
    handleClose: () => void;
}

function NormalModal({ show, handleClose }: IModalProps) {
    return (
        <>
            <Modal show={show} onHide={handleClose}>
                <Modal.Header closeButton>
                    <Modal.Title>Modal heading</Modal.Title>
                </Modal.Header>
                <Modal.Body> Lorem ipsum dolor sit amet consectetur. </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                    <Button variant="primary" onClick={handleClose}>
                        Save Changes
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
}

export default NormalModal;