import React, { useEffect, useState } from 'react'
import { Alert, Badge, Button, Col, Form, Modal, ModalBody, Row, Table } from 'react-bootstrap'
import Select from 'react-select'
import moment from 'moment'
import ReactDatePicker from 'react-datepicker'
import { PaymentService } from '../../services/payment.service'
import toast from 'react-hot-toast'
import { FaTimesCircle } from 'react-icons/fa'
import { StaticMutedText } from './text.modal'
import StatusLabel from '../Labels/status.label'

interface IMakeTransactionModal {
    show: boolean,
    handleClose: any,
}


export default function MakeTransactionModal(props: IMakeTransactionModal) {


    const [payout, setPayout] = useState<any>()

    const getPayoutById = async () => {
        await PaymentService.getPayoutbyId(props.show, true).then((res) => {
            if (res.status === 200) {
                setPayout(res.data.payout)
            }
        }).catch(e => {
            console.log(e)
            props.handleClose()
        })
    }


    useEffect(() => {
        if (props.show) {
            getPayoutById()
        }
    }, [props.show])

    const makeTransfer = async() => {
        await PaymentService.makeTransfer(props.show).then((res) => {
            if(res.status === 200) {
                toast.success("Transfer Made Successfully")
                props.handleClose()
            }
        }).catch(e => {
            console.log(e)
            toast.error("Something went wrong while sending request to RZP X")
        })
    }

    return (
        <Modal
            show={props.show ? true : false}
            onHide={() => { props.handleClose() }}
            size="xl"

        >
            <Modal.Body>
                <div className='d-flex justify-content-between align-items-center mb-3'>
                    <h5>
                        Make a transfer using RazorpayX
                    </h5>
                    <FaTimesCircle className="text-danger" onClick={props.handleClose} />
                </div>
                <Row>
                    <Col className="d-flex justify-content-start align-items-center">
                        <StaticMutedText label={"Therapist Name:"} value={payout?.therapistId?.name} />
                        {payout?.therapistId?.isVerified && <Badge className='ms-2' bg='success'>Verified</Badge> || <Badge className='ms-2' bg='danger'>Not Verified</Badge>}
                    </Col>
                    <Col className="d-flex justify-content-start align-items-center">
                        <StaticMutedText label={"UPI ID:"} value={payout?.therapistId?.bankDetails?.upiId || "--"} />
                        {payout?.therapistId?.bankDetails?.upiId && payout?.therapistId?.bankDetails?.upiApprove && <Badge className='ms-2' bg='success'>Approved</Badge> || <Badge className='ms-2' bg='danger'>Not Approved</Badge>}
                    </Col>
                </Row>

                <Row className='mt-2'>
                    <Col className="d-flex justify-content-start align-items-center">
                        <StaticMutedText label={"Amount:"} value={payout?.totalAmount} />
                    </Col>
                    <Col className="d-flex justify-content-start align-items-center">
                        <StaticMutedText label={"Commission:"} value={payout?.commission || "--"} />
                    </Col>
                </Row>

                <Row className='mt-2'>
                    <Col className="d-flex justify-content-start align-items-center">
                    <StatusLabel status={payout?.payoutStatus || "--"}/>
                    </Col>
                </Row>

                <hr />
                <Row className='mt-2'>
                    <Col md={6}>
                        <h5>
                            Invoices
                        </h5>
                        <Table striped hover>
                            <thead>
                                <tr>
                                    <th>
                                        S.No.
                                    </th>
                                    <th>
                                        Invoice ID
                                    </th>
                                    <th>
                                        Date
                                    </th>
                                    <th>
                                        Amount
                                    </th>

                                </tr>
                            </thead>
                            <tbody>
                                {payout?.invoices && payout?.invoices.length > 0 ? payout?.invoices?.map((invoice: any, index: number) => {
                                    return (
                                        <tr className='fw-medium'>
                                            <td>
                                                {index + 1}
                                            </td>
                                            <td>
                                                {invoice.invoiceSerialNumber}
                                            </td>
                                            <td>
                                                {moment(invoice.createdAt).format("DD MM YY")}
                                            </td>
                                            <td>
                                                {invoice.invoiceValue}
                                            </td>
                                        </tr>
                                    )
                                }) : <tr>
                                    <td colSpan={4}>
                                        No Invoices to show
                                    </td>
                                </tr>}
                            </tbody>
                        </Table>
                    </Col>
                    <Col md={6}>
                        <h5>
                            Deductions
                        </h5>
                        <Table striped hover>
                            <thead>
                                <tr>
                                    <th>
                                        S.No.
                                    </th>

                                    <th>
                                        Type
                                    </th>
                                    <th>
                                        Date
                                    </th>
                                    <th>
                                        Amount
                                    </th>

                                </tr>
                            </thead>
                            <tbody>
                                {payout?.deductions && payout?.deductions.length > 0 ? payout?.deductions?.map((deduction: any, index: number) => {
                                    return (
                                        <tr className='fw-medium'>
                                            <td>
                                                {index + 1}
                                            </td>
                                            <td>
                                                {deduction.deductionType}
                                            </td>
                                            <td>
                                                {moment(deduction.deductionDate).format("DD MM YY")}
                                            </td>
                                            <td>
                                                {deduction.amount}
                                            </td>
                                        </tr>
                                    )
                                }) : <tr>
                                    <td colSpan={4}>
                                        No Deductions to show
                                    </td>
                                </tr>}
                            </tbody>
                        </Table>
                    </Col>
                </Row>

                <Row>
                    <Col>
                        <Alert variant="warning">
                            <Alert.Heading>Important!</Alert.Heading>
                            <p>
                                Please check the details of the transaction before making the transfer. Transfer Once made cannot be reversed
                            </p>
                        </Alert>
                    </Col>
                </Row>
                <Row>
                    <Col>
                    <p className='text-muted'>
                    I Agree that i have view the details and i want to make the transfer of  <b className='text-danger'> Rs.{payout?.amountToBePaid}</b> to <b  className='text-danger'>{payout?.therapistId?.name} </b> on UPI Id <b  className='text-danger'>{payout?.therapistId?.bankDetails?.upiId}</b> using RazorpayX.
                    </p>
                    </Col>
                    
                </Row>
                <Row>
                    <Col>
                    <Button onClick={() => makeTransfer()}>
                        Make Transfer
                    </Button>
                    </Col>
                </Row>
            </Modal.Body>
        </Modal>
    )
}
