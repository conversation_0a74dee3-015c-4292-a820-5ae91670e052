import moment from "moment";
import { Table } from "react-bootstrap";


interface IDeductionTable {
    tableData: any
}

export default function ExportDataTable({ tableData }: IDeductionTable) {
    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Type</th>
                            <th>Id</th>
                            <th>Client Id</th>
                            <th>Total</th>
                            <th>Commission</th>
                            <th>Is Cancelled</th>
                            <th>Discount</th>
                            <th>Invoice Date</th>
                            <th>Invoice Amount</th>
                            <th>Payout Id</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData && tableData?.length > 0 ? tableData?.map((data: any, index: number) => {
                            return (
                                <tr key={index + 1}>
                                    <td>{index + 1}</td>
                                    <td>{data?.Type || "--"}</td>
                                    <td>{data['Invoice/Deduction Id'] || "--"}</td>
                                    <td>{data['Client Id'] || "--"}</td>
                                    <td>{data?.Total || "--"}</td>
                                    <td>{data?.Commission}</td>
                                    <td>{data?.['Cancellation INV']}</td>
                                    <td>{data?.Discount}</td>
                                    {/* <td>{format(new Date(data['Invoice/Deduction Date']), "dd-MM-yyyy") || "--"}</td> */}
                                    <td>{data['Invoice/Deduction Date']}</td>
                                    <td>{data?.Amount || "--"}</td>
                                    <td>{data['Payout Id/RZP Id(for deduction)'] || "--"}</td>
                                </tr>
                            )
                        }) : "No data found"}
                    </tbody>
                </Table>
            </div>
        </div>
    )
}