import { useEffect, useState } from "react";
import { Card, Col, Form, Row } from "react-bootstrap";
import Select from "react-select"
import { AdminServices } from "../../../services/admin.service";
import ClientTransactionTable from "../../../components/Table/ClientTransaction.table";
import TablePagination from "../../../components/Pagination/Table.paginaition";
import { TherapistService } from "../../../services/therapist.service";
import moment from "moment";
import DatePicker from "react-datepicker";


export default function ClientTransaction() {

    const [therapist, setTherapist] = useState<any>();
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [selectStatus, setSelectStatus] = useState<any>(undefined);
    const [transactions, setTransactions] = useState<any>();
    const [pageSize, setPageSize] = useState<number>(10);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [count, setCount] = useState<number>(0);

    const [startDate, setStartDate] = useState<any>(moment().subtract("1", "M").toDate());
    const [endDate, setEndDate] = useState<any>(new Date());

    const statuses = [
        {
            label: "Pending",
            value: "PENDING"
        },
        {
            label: "Complete",
            value: "COMPLETE"
        },
        {
            label: "Cancelled",
            value: "CANCELLED"
        },
        {
            label: "Paid Offline",
            value: "PAID OFFLINE"
        },
        {
            label: "Failed",
            value: "FAILED"
        }
    ]

    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(1, 999).then((res) => {
            if (res.status === 200) {
                setTherapist(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }



    const getAllTransactions = async () => {
        await AdminServices.getClientTransaction(selectTherapist, currentPage, pageSize, selectStatus, startDate?.toISOString(), endDate?.toISOString()).then((res) => {
            if (res.status === 200) {
                setTransactions(res.data?.clientTransactions || []);
                setCount(res.data?.count || 0);
            }
        }).catch(e => {
            console.log(e)
        })
    }

    useEffect(() => {
        getAllTherapist();
    }, [])

    useEffect(() => {
        getAllTransactions();
    }, [selectStatus, selectTherapist, currentPage, startDate, endDate])


    return (
        <>
            <h5>
                Client  Transactions
            </h5>

            <Row className="mt-5">
                <Col>
                    <h6>
                        Filters
                    </h6>
                </Col>
            </Row>

            <Row className="mt-3">
                <Col md={3}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Start Date</Form.Label>
                        <div>
                            <DatePicker className="form-control" selected={startDate} onChange={(date: any) => setStartDate(date)} dateFormat="dd/MM/yyyy"
                            />
                        </div>
                    </Form.Group>
                </Col>
                <Col md={3}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">End Date</Form.Label>
                        <div>
                            <DatePicker className="form-control" selected={endDate} onChange={(date: any) => setEndDate(date)} dateFormat="dd/MM/yyyy" />
                        </div>
                    </Form.Group>
                </Col>
                <Col md={3}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Select Therapist</Form.Label>
                        <Select
                            options={therapist}
                            onChange={(e: any, action: any) => {
                                if (action.action === 'clear') {
                                    setSelectTherapist(undefined);
                                    return;
                                }
                                setSelectTherapist(e.value)
                            }}
                            isClearable
                        />
                    </Form.Group>
                </Col>
                <Col md={3}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Select Status</Form.Label>
                        <Select
                            options={statuses}
                            onChange={(e: any, action: any) => {
                                if (action.action === 'clear') {
                                    setSelectStatus(undefined);
                                    return;
                                }
                                setSelectStatus(e.value)
                            }}
                            isClearable
                        />
                    </Form.Group>
                </Col>
            </Row>

            <Card className="mt-3 mb-3 shadow">
                <Row>
                    <Col>
                        <div className="mt-3 ms-2 fw-bold">All Transactions</div>
                        <div>
                            <ClientTransactionTable tableData={transactions} reload={getAllTransactions} />
                        </div>
                        <div className="bg-white py-2 px-3">
                            <TablePagination
                                total={count}
                                currentPage={currentPage}
                                perPage={pageSize}
                                handlePageChange={(e: number) => {
                                    setCurrentPage(e)
                                }}
                                setPerPage={(e: number) => { setPageSize(e) }}
                            />
                        </div>
                    </Col>
                </Row>
            </Card>
        </>
    )
}