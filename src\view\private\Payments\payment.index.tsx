import { Row, Col, <PERSON>, Card, But<PERSON>, Pagination } from 'react-bootstrap'
import DataCard from '../../../components/Cards/DataCard'
import DatePicker from "react-datepicker";
import ManualInvoiceModal from '../../../components/Modals/ManualInvoice.modal';
import { useEffect, useState } from 'react';
import PaymentTable from '../../../components/Table/Payment.table';
import { PaymentService } from '../../../services/payment.service';
import { TherapistService } from '../../../services/therapist.service';
import Select from "react-select"
import moment from 'moment';

export default function Payments() {

    const [invoice, setInvoice] = useState<any>();
    const [pageNumber, setPageNumber] = useState<number>(1)
    const [pageSize, setPageSize] = useState<number>(10)
    const [startDate, setStartDate] = useState<any>(moment().subtract("1", "M").toDate());
    const [endDate, setEndDate] = useState<any>(new Date());
    const [showManualInvoiceModal, setShowManualInvoiceModal] = useState<boolean>(false)
    const [stats, setStats] = useState<any>({});
    const [therapist, setTherapist] = useState<any>();
    const [selectTherapist, setSelectTherapist] = useState<any>();
    const [total, setTotal] = useState<number>(0);






    let items: any = [];

    for (let number = 1; number <= pageNumber; number++) {
        items.push(
            <Pagination.Item key={number} active={number === pageNumber}>
                {number}
            </Pagination.Item>
        );
    }

    const getAllInvoice = async () => {
        await PaymentService.getAllInvoices(pageNumber, pageSize, startDate?.toISOString(), endDate?.toISOString(), selectTherapist).then((res) => {
            if (res.status === 200) {
                setInvoice(res.data.invoices);
                setTotal(res.data.totalCount)
            }
        })
    }


    const getAllTherapist = async () => {
        await TherapistService.getAllTherapist(1, 999).then((res) => {
            if (res.status === 200) {
                setTherapist(res.data.map((therapist: any) => {
                    return {
                        label: therapist.name + " " + `(${therapist.email})`,
                        value: therapist._id
                    }
                }))
            }
        })
    }

    const getAllStats = async () => {
        await PaymentService.getInvoiceStats().then((res) => {
            if (res.status === 200) {
                setStats(res.data)
            }
        })
    }

    useEffect(() => {
        getAllInvoice();
    }, [pageNumber, pageSize, startDate, endDate, selectTherapist])

    useEffect(() => {
        getAllStats();
        getAllTherapist();
    }, [])





    return (
        <>
            <Row >
                <Col >
                    <DataCard title={"Total Invoices"} text={" "} subTitle={"Last week"} value={
                        stats?.totalInvoices
                    } color="#F5C8BD" />
                </Col>
                <Col >
                    <DataCard title={"Last 30 Days Invoices"} text={" "} subTitle={"Current Months"} value={stats?.last30DayInvoices} color="#E2BEE2" />
                </Col>
                <Col >
                    <DataCard title={"Last 30 Day Invoice Value"} text={" "} subTitle={"current month"} value={stats?.last30DayInvoiceValue} color="#9CBCE4" />
                </Col>
            </Row>

            <Row className="mt-3">
                <Col>
                    <h6>
                        Filters
                    </h6>
                </Col>
            </Row>
            <Row className="mt-2">
                <Col md={4}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Start Date</Form.Label>
                        <div>
                            <DatePicker className="form-control" selected={startDate} onChange={(date: any) => setStartDate(date)} dateFormat="dd/MM/yyyy"
                            />
                        </div>
                    </Form.Group>
                </Col>
                <Col md={4}>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">End Date</Form.Label>
                        <div>
                            <DatePicker className="form-control" selected={endDate} onChange={(date: any) => setEndDate(date)} dateFormat="dd/MM/yyyy" />
                        </div>
                    </Form.Group>
                </Col>
                <Col>
                    <Form.Group className="p-2 mb-3 card shadow border-0">
                        <Form.Label className="text-muted">Select Therapist</Form.Label>
                        <Select
                            options={therapist}
                            onChange={(e: any, action: any) => {
                                if (action.action === 'clear') {
                                    setSelectTherapist(undefined);
                                    return;
                                }
                                setSelectTherapist(e.value)
                            }}
                            isClearable
                        />
                    </Form.Group>
                </Col>
            </Row>
            <Row className="mt-3">
                <div className="d-flex justify-content-between align-items-center">
                    <span className=" fw-bold">
                        Invoices:
                    </span>
                    <Button size="sm" variant="secondary" onClick={() => setShowManualInvoiceModal(true)}>Create Invoice</Button>
                </div>
                <div>
                    <PaymentTable tableData={invoice}
                        getAllInvoices={getAllInvoice}
                    />
                </div>
                <div className="px-0 px-md-3 mt-2">
                    <div className="d-flex flex-column flex-md-row justify-content-md-between">
                        <div>
                            <Pagination>
                                <Pagination.Prev onClick={() => setPageNumber(pageNumber - 1)} />
                                {items}
                                <Pagination.Next onClick={() => setPageNumber(pageNumber + 1)} />
                            </Pagination>
                        </div>
                        <div>
                            <div className='d-flex align-items-center gap-2'>
                                <p className="small mb-0">
                                    Showing {pageNumber * pageSize - pageSize + 1} -{" "}
                                    {Math.min(pageNumber * pageSize, total)} of{" "}
                                    {total} results
                                </p>
                                <div className="col-auto">
                                    <select className="form-select bg-light form-select-sm" aria-label=".form-select-sm example" onChange={(e: any) => { setPageSize(e.target.value) }}>

                                        <option value="10">10</option>
                                        <option value="15">15</option>
                                        <option value="20">20</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Row>

            <ManualInvoiceModal
                show={showManualInvoiceModal}
                handleClose={() => {
                    setShowManualInvoiceModal(false);
                    getAllInvoice();
                }}
            />
        </>
    )
}