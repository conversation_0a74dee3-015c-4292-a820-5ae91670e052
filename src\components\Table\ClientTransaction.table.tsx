import { Dropdown, Table } from "react-bootstrap"
import { FaCashRegister, FaEllipsis<PERSON>, FaEye, FaFileInvoiceDollar, FaSync } from "react-icons/fa";
import CustomToggle from "../Menu/CustomMenu";
import { useNavigate } from "react-router-dom";
import PayoutUpdateModal from "../Modals/PayoutUpdateModal";
import React, { useEffect } from "react";
import StatusLabel from "../Labels/status.label";
import moment from "moment";
import MakeTransactionModal from "../Modals/MakeTransaction.modal";
import { TherapistService } from "../../services/therapist.service";
import toast from "react-hot-toast";

interface IPayoutTable {
    tableData: any,
    reload: any
}

export default function ClientTransactionTable({ tableData, reload }: IPayoutTable) {

    const navigate = useNavigate();


    const [showUpdateModal, setShowUpdateModal] = React.useState<any>(undefined);
    const [makeTherapistPayout, setMakeTherapistPayout] = React.useState<any>(undefined);

    const refresh = async (id: any) => {
        await TherapistService.paymentVerify(id).then((res) => {
            if (res.status === 200) {
                reload()
            }
        }).catch(err => {
            toast.error(err.response.data)
        })
    }
    useEffect(() => {
        reload();
    }, [showUpdateModal])

    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead style={{ textAlign: "center" }}>
                        <tr>
                            <th>Sr.No</th>
                            <th>Customer/Session Time</th>
                            <th>RZP Ref</th>
                            <th>Therapist</th>
                            <th>Amount</th>
                            <th>Gateway Charges</th>
                            <th>Status/Created At</th>
                            {/* <th>Action</th> */}
                        </tr>
                    </thead>
                    <tbody style={{ textAlign: "center" }}>
                        {tableData && tableData.length > 0 ? tableData.map((data: any, index: number) => {
                            return (
                                <tr className="fs-small">
                                    <td className="text-muted">{index + 1}</td>
                                    <td className="text-primary">
                                        <div>
                                            {data?.clientId?.clientId || "--"}
                                        </div>
                                        <div className="text-secondary">
                                            {data?.fromDate ? moment(data?.fromDate).format("DD-MM-YYYY hh:mm a") : "--"}
                                        </div>
                                    </td>
                                    <th><div>{data?._id}</div> <a href={data?.paymentLink}>{data?.paymentLink || "--"}</a></th>
                                    <td className="text-primary"><div>{data?.therapistId?.name || "--"}</div> <div>{data?.therapistId?.email || "--"}</div></td>
                                    <td className={`fw-bold ${data.amount > 0 ? "text-success" : "text-danger"}`}> {data?.amount || "--"}</td>
                                    <td className="text-primary">
                                        <div>{data?.paymentStatus == "COMPLETE" ? <>Fee: {data?.gatewayCharges?.gatewayFee || "--"}, Tax: {data?.gatewayCharges?.gatewayTax || "--"}</> : "--"}</div>
                                    </td>
                                    <td>
                                        <StatusLabel status={data?.paymentStatus} /> 
                                        <div>{moment(data.createdAt).format("DD-MM-YYYY hh:mm:a")}</div>
                                    </td>
                                    <td>
                                        <Dropdown>
                                            <Dropdown.Toggle
                                                as={CustomToggle}
                                                id="dropdown-custom-components"
                                            >
                                                <FaEllipsisV className="cursor-pointer" />
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu>
                                                <Dropdown.Item
                                                    onClick={() => refresh(data._id)}
                                                >
                                                    <FaCashRegister className="text-muted me-2" />
                                                    <span className="fw-bold text-secondary fs-12">
                                                        Refresh
                                                    </span>
                                                </Dropdown.Item>
                                            </Dropdown.Menu>
                                        </Dropdown>
                                    </td>
                                </tr>
                            )
                        }) : <tr>
                            <td colSpan={10} className="fw-bold text-muted">
                                No Transactions to show
                            </td>
                        </tr>
                        }
                    </tbody>
                </Table>
            </div>
            <PayoutUpdateModal
                show={showUpdateModal}
                handleClose={() => setShowUpdateModal(undefined)}

            />

            <MakeTransactionModal
                show={makeTherapistPayout}
                handleClose={() => setMakeTherapistPayout(undefined)}
            />
        </div >
    )
}