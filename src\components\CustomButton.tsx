
import { Button } from 'react-bootstrap'
interface ScheduleButtonProps {
  onClick?: () => void;
  title: string
}


const CustomButton = ({
  onClick,
  title
}: ScheduleButtonProps) => {
  return (
    <>
      <Button
        onClick={onClick}
        className="m-2"
        style={{ background: "#E07446" }}
      >
        <span className="pe-2">{title} </span>
      </Button>
    </>
  )
}

export default CustomButton