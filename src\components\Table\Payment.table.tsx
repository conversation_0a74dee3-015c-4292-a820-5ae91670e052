import { Badge, Dropdown, Table } from "react-bootstrap"
import { FaEllipsisV, FaEye } from "react-icons/fa";
import CustomToggle from "../Menu/CustomMenu";
import { useNavigate } from "react-router-dom";
import { PaymentService } from "../../services/payment.service";
import toast from "react-hot-toast";
import { MdDelete } from "react-icons/md";

interface ITherapistTable {
    tableData: any
    getAllInvoices: any
}

export default function PaymentTable({ tableData, getAllInvoices }: ITherapistTable) {

    const navigate = useNavigate();


    const deleteInvoice = async (id: any) => {
        await PaymentService.deleteInvoice(id).then((res) => {
            if (res.status === 200) {
                getAllInvoices();
                toast.success("Deduction deleted successfully")
            }
        }
        )
    }
    const config = ["CGST", "SGST", "IGST"]

    return (
        <div className="border rounded grid-border mt-2">
            <div className="d-none d-lg-block">
                <Table hover responsive className="resposive-table ">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Therapist Name</th>
                            <th>S No.</th>
                            <th>Client ID</th>
                            <th>Item Value</th>
                            <th>Invoice Value</th>
                            <th>Discount</th>
                            <th className="text-center" colSpan={config.length}>GST</th>
                            <th>Payout</th>
                            <th>C.Inv.</th>
                            <th>Gateway Charges</th>

                            <th>Action</th>
                        </tr>
                        <tr className="text-primary xcn-text-10">
                            <th colSpan={7}></th>
                            {config.map((data) => {
                                return <th className="text-center fs-12">{data}</th>;
                            })}
                            <th colSpan={3}></th>

                        </tr>
                    </thead>
                    <tbody>
                        {tableData && tableData.length > 0 ? tableData.map((data: any, index: number) => {
                            return (
                                <tr>
                                    <td>{index + 1}</td>
                                    <td className="fs-6 fw-bold">{data?.therapistId?.name || "--"}</td>
                                    <td className="text-primary" style={{ fontSize: "12px" }}>{data?.invoiceSerialNumber || "--"}</td>
                                    <td>{data?.clientId?.clientId || "--"}</td>
                                    <td>{data?.itemTotal || "--"}</td>
                                    <td>{data?.invoiceValue || "--"}</td>
                                    <td>₹{data?.discount.toFixed(2)}</td>
                                    {data?.gst && Object.values(data?.gst).map((data: any) => {
                                        return (
                                            <td>
                                                ₹{data.toFixed(2)}
                                            </td>
                                        )
                                    })}
                                    <td className="fw-bold text-primary">
                                        {data?.payout ? "Paid" : "Pending"}
                                    </td>
                                    <td>{data.isCancelInvoice ? <Badge bg="success">Yes</Badge> : <Badge bg="danger">No</Badge>}</td>
                                    <td className="text-primary">Fee: {data?.gatewayCharges?.gatewayFee || 0}, Tax: {data?.gatewayCharges?.gatewayTax || 0}</td>
                                    <td style={{ cursor: "pointer" }}>
                                        <Dropdown>
                                            <Dropdown.Toggle
                                                as={CustomToggle}
                                                id="dropdown-custom-components"
                                            >
                                                <FaEllipsisV className="cursor-pointer" />
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu>
                                                {/* <Dropdown.Item
                                                    onClick={() => navigate("/therapist/" + data.therapistId._id)}
                                                >
                                                    <FaEye className="text-secondary" />{" "}{" "}
                                                    <span className="fw-bold text-secondary fs-12">
                                                        View
                                                    </span>
                                                </Dropdown.Item> */}
                                                <Dropdown.Item
                                                    onClick={
                                                        () => {
                                                            deleteInvoice(data._id)

                                                        }
                                                    }
                                                >
                                                    <MdDelete className="text-secondary" />{" "}{" "}
                                                    <span className="fw-bold text-secondary fs-12">
                                                        delete
                                                    </span>
                                                </Dropdown.Item>

                                            </Dropdown.Menu>
                                        </Dropdown>
                                    </td>
                                </tr>
                            )
                        }) : "No data found"}
                    </tbody>
                </Table>
            </div>
        </div>
    )
}