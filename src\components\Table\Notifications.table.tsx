import moment from 'moment'
import React from 'react'
import { Button, Table } from 'react-bootstrap'

type TableProps = {
  tableData: any[];
  handleDelete: (id: string) => void
}

export default function NotificationsTable({ tableData, handleDelete }: TableProps) {

  return (
    <>
      <Table responsive striped hover size="sm">
        <thead>
          <tr>
            <th>
              S/N
            </th>
            <th>
              Message
            </th>
            <th>
              Created At
            </th>
            <th>
              Action
            </th>
          </tr>

        </thead>
        <tbody>
          {tableData && tableData.length > 0 ?
            tableData.map((notification: any, index: number) => {
              return (
                <tr key={notification._id}>
                  <td>{index + 1}</td>
                  <td>{notification?.message}</td>
                  <td>{moment(notification.createdAt).format("DD/MM/YYYY hh:mm a")}</td>
                  <td>
                    <Button
                      variant='outline-danger'
                      size='sm'
                      onClick={() => handleDelete(notification._id)}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              )
            }) : <tr><td colSpan={4} className='text-center'>No Notification</td></tr>}
        </tbody>
      </Table>
    </>
  )
}