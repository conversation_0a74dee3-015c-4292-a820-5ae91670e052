import React from 'react'
import { Offcanvas, Button, Row, Col, Badge } from 'react-bootstrap'
import toast from 'react-hot-toast';

type IProps = {
  show: boolean;
  handleClose: () => void;
  data: any[];
}

export default function ClientListModal({ show, handleClose, data }: IProps) {
  function handleClick(data:any) {
    navigator.clipboard.writeText(data);
    toast.success("Client Id Copied to clipboard");
  }
  return (
    <>
      <Offcanvas show={show} onHide={handleClose} placement="end" >
        <Offcanvas.Header closeButton>
          <Offcanvas.Title>Clients</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body>
          <Row>
            {
              data?.map((client: any) => {
                return (
                  <Col className="col-auto d-md-inline-flex mb-2 ">
                    {/* <Button>{client.clientId}</Button> */}
                    <Badge bg="secondary" className='p-2' style={{ cursor: "pointer" }} pill onClick={handleClick}>{client.clientId}</Badge>
                  </Col>
                )
              })
            }

          </Row>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  )
}