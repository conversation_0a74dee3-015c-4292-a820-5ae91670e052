import { Card } from 'react-bootstrap'

interface DataCardProps {
    title: string;
    text?: string;
    subTitle: string;
    value: string;
    color: string;
}
const DataCard = ({
    title, subTitle, text, value, color
}: DataCardProps) => {
    return (
        <Card style={{
            width: '18rem',
            border: `1px solid ${color}`,
            padding: '0',
        }}>
            <Card.Body className='p-0 mt-3'>
                <Card.Title className='ps-3'>{value}</Card.Title>
                <Card.Subtitle className=" ps-3 mb-2 text-bold">{title}</Card.Subtitle>
                <Card.Text className='text-muted ps-3'>
                    {subTitle}
                </Card.Text>
                <Card.Text style={{
                    background: color,
                    paddingLeft: '20px',
                    paddingRight: '20px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    minHeight: '10px',
                }}>
                    {text}
                </Card.Text>
            </Card.Body>
        </Card>
    )
}

export default DataCard;