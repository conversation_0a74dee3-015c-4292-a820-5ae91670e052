import React from 'react'
import { But<PERSON>, Modal } from 'react-bootstrap'
import { FaTrash } from 'react-icons/fa'
import { TherapistService } from '../../services/therapist.service'
import toast from 'react-hot-toast'

interface IConfirmationModal {
    show: any,
    handleClose: any,
    reload: any
}

export default function ConfirmationModal(props: IConfirmationModal) {


    const handleDeletePendingLinks = async () => {
        await TherapistService.deletePendingLinks(props.show).then((res) => {
            if (res.status === 200) {
                props.reload();
                props.handleClose();
            }
        }).catch(err => {
            toast.error(err.response.data)
        })
    }

    return (
        <>
            <Modal
                show={props.show ? true : false}
                onHide={props.handleClose}
                backdrop="static"
                keyboard={false}
                animation={true}
                centered
            >
                <Modal.Body>
                    <div className="d-flex flex-column justify-content-center align-items-center mt-5">
                        <div>
                            <FaTrash className="text-danger" style={{ fontSize: 40 }} />
                        </div>
                        <div className="mt-3">
                            <p className="text-secondary fw-bold">Are you sure want to delete this pending link.</p>
                        </div>
                    </div>
                    <div className="d-flex justify-content-end gap-3 mt-3">
                        <Button size="sm" variant='danger' className="fw-bold" onClick={handleDeletePendingLinks}>Delete</Button>
                        <Button size="sm" variant='secondary' className="fw-bold" onClick={props.handleClose}>Cancel</Button>
                    </div>
                </Modal.Body>
            </Modal>
        </>
    )
}
